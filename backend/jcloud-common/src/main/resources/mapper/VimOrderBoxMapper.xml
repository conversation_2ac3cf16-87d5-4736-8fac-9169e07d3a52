<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.VimOrderBoxMapper">

    <!-- 按日期和业务类型统计收入数据 -->
    <select id="getBusinessRevenueByDateAndType" resultType="com.jcloud.common.mapper.VimOrderBoxMapper$BusinessRevenueStats">
        SELECT 
            DATE(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000)) as date,
            type as businessType,
            COUNT(*) as orderCount,
            SUM(price) as totalAmount,
            AVG(price) as avgAmount
        FROM vim_order_box 
        WHERE timestamp IS NOT NULL
          AND CAST(timestamp AS SIGNED) &gt;= #{startTimestamp}
          AND CAST(timestamp AS SIGNED) &lt;= #{endTimestamp}
          AND price IS NOT NULL
          AND type IS NOT NULL
        GROUP BY DATE(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000)), type
        ORDER BY date DESC, businessType
    </select>

    <!-- 按业务类型统计总收入 -->
    <select id="getBusinessTypeStats" resultType="com.jcloud.common.mapper.VimOrderBoxMapper$BusinessTypeStats">
        SELECT 
            type as businessType,
            CASE type
                WHEN 1 THEN '开箱'
                WHEN 2 THEN '兑换'
                WHEN 3 THEN '对战'
                WHEN 5 THEN '升级'
                WHEN 6 THEN '补偿'
                WHEN 7 THEN 'Roll房'
                WHEN 8 THEN '奖励'
                WHEN 9 THEN '融合'
                ELSE '其他'
            END as businessTypeName,
            COUNT(*) as orderCount,
            SUM(price) as totalAmount,
            ROUND(SUM(price) * 100.0 / (
                SELECT SUM(price) 
                FROM vim_order_box 
                WHERE timestamp IS NOT NULL
                  AND CAST(timestamp AS SIGNED) &gt;= #{startTimestamp}
                  AND CAST(timestamp AS SIGNED) &lt;= #{endTimestamp}
                  AND price IS NOT NULL
                  AND type IS NOT NULL
            ), 2) as percentage
        FROM vim_order_box 
        WHERE timestamp IS NOT NULL
          AND CAST(timestamp AS SIGNED) &gt;= #{startTimestamp}
          AND CAST(timestamp AS SIGNED) &lt;= #{endTimestamp}
          AND price IS NOT NULL
          AND type IS NOT NULL
        GROUP BY type
        ORDER BY totalAmount DESC
    </select>

    <!-- 查询开箱消费统计数据 -->
    <select id="getBoxConsumptionStats" resultType="com.jcloud.common.mapper.VimOrderBoxMapper$BoxConsumptionStats">
        SELECT
            DATE(FROM_UNIXTIME(CAST(vob.timestamp AS SIGNED)/1000)) as date,
            COUNT(*) as boxCount,
            SUM(vob.price) as boxAmount,
            SUM(COALESCE(vi.price_cost, vob.price, 0)) as itemValue,
            COUNT(DISTINCT vob.uid) as userCount
        FROM vim_order_box vob
        LEFT JOIN vim_item vi ON vob.itemid = vi.id
        WHERE vob.timestamp IS NOT NULL
          AND CAST(vob.timestamp AS SIGNED) &gt;= #{startTimestamp}
          AND CAST(vob.timestamp AS SIGNED) &lt;= #{endTimestamp}
          AND vob.price IS NOT NULL
          AND vob.type = 1
        GROUP BY DATE(FROM_UNIXTIME(CAST(vob.timestamp AS SIGNED)/1000))
        ORDER BY date DESC
    </select>



    <!-- 查询用户行为散点图数据 -->
    <select id="getUserBehaviorStats" resultType="com.jcloud.common.mapper.VimOrderBoxMapper$UserBehaviorStats">
        SELECT 
            uid,
            COUNT(*) as totalOrders,
            SUM(price) as totalSpent,
            AVG(price) as avgOrderValue,
            CASE
                WHEN COUNT(*) &gt;= 100 THEN 'vip'
                WHEN COUNT(*) &gt;= 50 THEN 'active'
                ELSE 'normal'
            END as userType,
            CASE
                WHEN SUM(price) &gt;= 10000 THEN 5
                WHEN SUM(price) &gt;= 5000 THEN 4
                WHEN SUM(price) &gt;= 2000 THEN 3
                WHEN SUM(price) &gt;= 500 THEN 2
                ELSE 1
            END as userLevel
        FROM vim_order_box 
        WHERE timestamp IS NOT NULL
          AND CAST(timestamp AS SIGNED) &gt;= #{startTimestamp}
          AND CAST(timestamp AS SIGNED) &lt;= #{endTimestamp}
          AND price IS NOT NULL
          AND uid IS NOT NULL
        GROUP BY uid
        HAVING COUNT(*) &gt;= 5
        ORDER BY totalSpent DESC
        LIMIT 1000
    </select>



    <!-- 查询用户活跃度热力图数据 -->
    <select id="getUserActivityStats" resultType="com.jcloud.common.mapper.VimOrderBoxMapper$UserActivityStats">
        SELECT 
            DATE(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000)) as date,
            HOUR(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000)) as hour,
            COUNT(DISTINCT uid) as activeUsers,
            SUM(price) as transactionAmount
        FROM vim_order_box 
        WHERE timestamp IS NOT NULL
          AND CAST(timestamp AS SIGNED) &gt;= #{startTimestamp}
          AND CAST(timestamp AS SIGNED) &lt;= #{endTimestamp}
          AND price IS NOT NULL
          AND uid IS NOT NULL
        GROUP BY 
            DATE(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000)),
            HOUR(FROM_UNIXTIME(CAST(timestamp AS SIGNED)/1000))
        ORDER BY date DESC, hour
    </select>

</mapper>
