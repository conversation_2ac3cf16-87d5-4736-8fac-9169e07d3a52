<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.VimOrderRechargeMapper">

    <!-- 查询指定用户列表在指定时间范围内的成功充值记录 -->
    <select id="selectSuccessRechargesByUsers" resultType="com.jcloud.common.entity.VimOrderRecharge">
        SELECT * FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        ORDER BY uid, create_time DESC
    </select>

    <!-- 统计指定用户列表在指定时间范围内的充值总金额 -->
    <select id="sumRechargeAmountByUsers" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
    </select>

    <!-- 批量查询用户信息 -->
    <select id="selectByUserIds" resultType="com.jcloud.common.entity.VimUser">
        SELECT * FROM vim_user 
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND state = 1
        ORDER BY id
    </select>

    <!-- 按日期统计充值趋势数据 -->
    <select id="getRechargeTrendStats" resultType="com.jcloud.common.mapper.VimOrderRechargeMapper$RechargeTrendStats">
        SELECT
            DATE(FROM_UNIXTIME(create_time)) as date,
            COUNT(*) as rechargeCount,
            SUM(amount) as totalAmount,
            AVG(amount) as avgAmount
        FROM vim_order_recharge
        WHERE create_time IS NOT NULL
          AND create_time &gt;= #{startTimestamp}
          AND create_time &lt;= #{endTimestamp}
          AND amount IS NOT NULL
          AND state = 2
        GROUP BY DATE(FROM_UNIXTIME(create_time))
        ORDER BY date DESC
    </select>

    <!-- 查询用户充值行为统计数据 -->
    <select id="getUserBehaviorStats" resultType="com.jcloud.common.mapper.VimOrderRechargeMapper$UserBehaviorStats">
        SELECT
            uid as userId,
            CONCAT('用户', LPAD(uid, 6, '0')) as userName,
            COUNT(*) as rechargeCount,
            SUM(amount) as totalAmount,
            AVG(amount) as avgAmount,
            CASE
                WHEN SUM(amount) >= 10000 THEN 10
                WHEN SUM(amount) >= 5000 THEN 9
                WHEN SUM(amount) >= 2000 THEN 8
                WHEN SUM(amount) >= 1000 THEN 7
                WHEN SUM(amount) >= 500 THEN 6
                WHEN SUM(amount) >= 200 THEN 5
                WHEN SUM(amount) >= 100 THEN 4
                WHEN SUM(amount) >= 50 THEN 3
                WHEN SUM(amount) >= 20 THEN 2
                ELSE 1
            END as userLevel,
            FROM_UNIXTIME(MAX(create_time), '%Y-%m-%d %H:%i:%s') as lastRechargeTime,
            CASE
                WHEN SUM(amount) >= 5000 THEN 'premium'
                WHEN SUM(amount) >= 1000 THEN 'vip'
                ELSE 'normal'
            END as userType,
            LEAST(100, GREATEST(50,
                ROUND(50 + (COUNT(*) * 5) + (SUM(amount) / 100))
            )) as activityScore
        FROM vim_order_recharge
        WHERE state = 2
          AND create_time &gt;= #{startTimestamp}
          AND create_time &lt;= #{endTimestamp}
        GROUP BY uid
        HAVING COUNT(*) &gt;= 1 AND SUM(amount) &gt; 0
        ORDER BY totalAmount DESC
        LIMIT 200
    </select>

</mapper>
