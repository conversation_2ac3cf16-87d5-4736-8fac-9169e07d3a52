<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.VimBattleMapper">

    <!-- 按日期统计对战收入数据 -->
    <select id="getBattleRevenueByDate" resultType="com.jcloud.common.mapper.VimBattleMapper$BattleRevenueStats">
        SELECT 
            DATE(FROM_UNIXTIME(create_time)) as date,
            COUNT(*) as battleCount,
            SUM(price) as totalRevenue,
            AVG(price) as avgRevenue
        FROM vim_battle 
        WHERE create_time IS NOT NULL 
          AND create_time &gt;= #{startTimestamp}
          AND create_time &lt;= #{endTimestamp}
          AND price IS NOT NULL
          AND state = 3
        GROUP BY DATE(FROM_UNIXTIME(create_time))
        ORDER BY date DESC
    </select>

</mapper>
