package com.jcloud.common.enums;

import lombok.Getter;

/**
 * 批量操作事务模式
 *
 * <p>基于性能分析，严格模式采用真正的批量操作，性能优于宽松模式：</p>
 * <ul>
 *   <li>严格模式：2次数据库写入操作（批量插入用户 + 批量分配角色）</li>
 *   <li>宽松模式：N×(2+M)次数据库写入操作（N为用户数，M为每用户角色数）</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum BatchTransactionMode {

    /**
     * 严格模式：全部成功或全部失败（推荐，性能最佳）
     *
     * <p><strong>性能优势：</strong></p>
     * <ul>
     *   <li>使用真正的批量操作（insertBatch）</li>
     *   <li>数据库写入次数最少（仅2次）</li>
     *   <li>执行速度快，适合大批量数据</li>
     * </ul>
     *
     * <p><strong>适用场景：</strong></p>
     * <ul>
     *   <li>数据质量较高，失败率低的场景</li>
     *   <li>对性能要求高的批量操作</li>
     *   <li>对数据一致性要求极高的场景</li>
     * </ul>
     */
    STRICT("严格模式", "全部成功或全部失败（推荐，性能最佳）");
    
    private final String displayName;
    private final String description;

    BatchTransactionMode(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取默认的事务模式
     *
     * <p>基于性能分析，默认使用严格模式：</p>
     * <ul>
     *   <li>真正的批量操作，性能优异</li>
     *   <li>数据库写入次数最少</li>
     *   <li>适合大多数批量操作场景</li>
     * </ul>
     *
     * @return 默认的事务模式（STRICT）
     */
    public static BatchTransactionMode getDefault() {
        return STRICT;
    }

    /**
     * 判断是否为严格模式
     *
     * @return 如果是严格模式返回true，否则返回false
     */
    public boolean isStrict() {
        return this == STRICT;
    }
}
