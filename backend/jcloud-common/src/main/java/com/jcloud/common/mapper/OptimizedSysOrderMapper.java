package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysOrder;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.jcloud.common.entity.table.SysOrderTableDef.SYS_ORDER;

/**
 * 优化的订单Mapper接口
 * 使用MyBatis-Flex QueryWrapper替代XML映射，提升性能和可维护性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface OptimizedSysOrderMapper extends BaseMapper<SysOrder> {
    
    /**
     * 检查订单是否存在（使用QueryWrapper）
     * 替代XML中的复杂条件查询
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否存在
     */
    default boolean existsOrderByUserAndTimeRange(String userId, Long startTime, Long endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.PAYID)
            .from(SYS_ORDER)
            .where(SYS_ORDER.USER_ID.eq(userId))
            .and(SYS_ORDER.START_TIME.le(endTime))
            .and(SYS_ORDER.END_TIME.ge(startTime))
            .limit(1);
        
        return selectOneByQuery(queryWrapper) != null;
    }
    
    /**
     * 统计用户订单数量（按状态）
     * 使用聚合函数替代复杂的统计查询
     * 
     * @param userId 用户ID
     * @return 订单数量
     */
    default long countOrdersByUser(String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.PAYID.count())
            .from(SYS_ORDER)
            .where(SYS_ORDER.USER_ID.eq(userId)
                .or(SYS_ORDER.AGENT.eq(userId)));
        
        return selectCountByQuery(queryWrapper);
    }
    
    /**
     * 统计用户特定状态的订单数量
     * 
     * @param userId 用户ID
     * @param status 订单状态
     * @return 订单数量
     */
    default long countOrdersByUserAndStatus(String userId, Integer status) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.PAYID.count())
            .from(SYS_ORDER)
            .where(SYS_ORDER.USER_ID.eq(userId)
                .or(SYS_ORDER.AGENT.eq(userId)))
            .and(SYS_ORDER.ORDER_STATUS.eq(status));
        
        return selectCountByQuery(queryWrapper);
    }
    
    /**
     * 统计用户订单金额（按字段）
     * 
     * @param userId 用户ID
     * @param amountField 金额字段名
     * @return 总金额
     */
    default BigDecimal sumAmountByUser(String userId, String amountField) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.TOTAL_AMOUNT.sum()) // 根据amountField动态选择
            .from(SYS_ORDER)
            .where(SYS_ORDER.USER_ID.eq(userId)
                .or(SYS_ORDER.AGENT.eq(userId)));
        
        // 根据字段名动态构建查询
        switch (amountField) {
            case "commission_amount":
                queryWrapper = QueryWrapper.create()
                    .select(SYS_ORDER.COMMISSION_AMOUNT.sum())
                    .from(SYS_ORDER)
                    .where(SYS_ORDER.USER_ID.eq(userId)
                        .or(SYS_ORDER.AGENT.eq(userId)));
                break;
            case "actual_amount":
                queryWrapper = QueryWrapper.create()
                    .select(SYS_ORDER.ACTUAL_AMOUNT.sum())
                    .from(SYS_ORDER)
                    .where(SYS_ORDER.USER_ID.eq(userId)
                        .or(SYS_ORDER.AGENT.eq(userId)));
                break;
            default:
                // 默认使用total_amount
                break;
        }
        
        Map<String, Object> result = selectOneByQueryAs(queryWrapper, Map.class);
        Object sum = result.values().iterator().next();
        return sum != null ? new BigDecimal(sum.toString()) : BigDecimal.ZERO;
    }
    
    /**
     * 查询主订单及其子订单（优化版本）
     * 使用CTE（公共表表达式）优化递归查询
     * 
     * @param queryWrapper 查询条件
     * @return 主订单列表
     */
    default List<SysOrder> selectMainOrdersWithSubOrdersOptimized(QueryWrapper queryWrapper) {
        // 使用CTE优化递归查询
        QueryWrapper cteQuery = QueryWrapper.create()
            .withRecursive("order_tree").asSelect(
                // 基础查询：主订单
                QueryWrapper.create()
                    .select(SYS_ORDER.ALL_COLUMNS, "0 as level")
                    .from(SYS_ORDER)
                    .where(SYS_ORDER.PARENTS_PAYID.isNull())
                    .union(
                        // 递归查询：子订单
                        QueryWrapper.create()
                            .select(SYS_ORDER.ALL_COLUMNS, "level + 1")
                            .from(SYS_ORDER)
                            .innerJoin("order_tree").on("order_tree.payid = sys_order.parents_payid")
                    )
            )
            .select()
            .from("order_tree")
            .orderBy("level", "create_time");
        
        return selectListByQuery(cteQuery);
    }
    
    /**
     * 分页查询订单（带性能优化）
     * 使用索引提示和查询优化
     * 
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    default Page<SysOrder> paginateOptimized(Page<SysOrder> page, QueryWrapper queryWrapper) {
        // 添加索引提示（如果需要）
        // queryWrapper.hint("USE INDEX (idx_user_time)");
        
        // 确保查询条件使用索引
        if (!queryWrapper.getWhereQueryCondition().getChildren().isEmpty()) {
            // 优化查询条件顺序，将选择性高的条件放在前面
            queryWrapper.orderBy(SYS_ORDER.CREATE_TIME.desc());
        }
        
        return paginate(page, queryWrapper);
    }
    
    /**
     * 批量更新订单状态（优化版本）
     * 使用CASE WHEN进行批量更新
     * 
     * @param orderIds 订单ID列表
     * @param status 新状态
     * @return 更新数量
     */
    default int batchUpdateStatus(List<String> orderIds, Integer status) {
        if (orderIds.isEmpty()) {
            return 0;
        }
        
        QueryWrapper updateWrapper = QueryWrapper.create()
            .set(SYS_ORDER.ORDER_STATUS, status)
            .set(SYS_ORDER.UPDATE_TIME, System.currentTimeMillis() / 1000)
            .where(SYS_ORDER.PAYID.in(orderIds));
        
        return updateByQuery(null, updateWrapper);
    }
    
    /**
     * 复杂统计查询：按时间段统计订单
     * 使用窗口函数和分组查询
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    default List<Map<String, Object>> getOrderStatsByTimeRange(Long startTime, Long endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                "DATE(FROM_UNIXTIME(create_time)) as order_date",
                SYS_ORDER.ORDER_STATUS,
                SYS_ORDER.PAYID.count().as("order_count"),
                SYS_ORDER.TOTAL_AMOUNT.sum().as("total_amount"),
                SYS_ORDER.COMMISSION_AMOUNT.sum().as("total_commission"),
                SYS_ORDER.ACTUAL_AMOUNT.sum().as("total_actual_amount")
            )
            .from(SYS_ORDER)
            .where(SYS_ORDER.CREATE_TIME.between(startTime, endTime))
            .groupBy("order_date", SYS_ORDER.ORDER_STATUS)
            .orderBy("order_date", SYS_ORDER.ORDER_STATUS);
        
        return selectListByQueryAs(queryWrapper, Map.class);
    }
    
    /**
     * 查询热门代理（按佣金排序）
     * 使用聚合函数和排序优化
     * 
     * @param limit 限制数量
     * @return 代理统计列表
     */
    default List<Map<String, Object>> getTopAgentsByCommission(int limit) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                SYS_ORDER.AGENT,
                SYS_ORDER.PAYID.count().as("order_count"),
                SYS_ORDER.COMMISSION_AMOUNT.sum().as("total_commission"),
                SYS_ORDER.ACTUAL_AMOUNT.sum().as("total_actual_amount")
            )
            .from(SYS_ORDER)
            .where(SYS_ORDER.AGENT.isNotNull())
            .and(SYS_ORDER.ORDER_STATUS.eq(1)) // 已结算
            .groupBy(SYS_ORDER.AGENT)
            .having(SYS_ORDER.COMMISSION_AMOUNT.sum().gt(0))
            .orderBy(SYS_ORDER.COMMISSION_AMOUNT.sum().desc())
            .limit(limit);
        
        return selectListByQueryAs(queryWrapper, Map.class);
    }
    
    /**
     * 查询订单趋势（按月统计）
     * 使用日期函数和分组查询
     * 
     * @param months 月份数
     * @return 趋势数据
     */
    default List<Map<String, Object>> getOrderTrendByMonth(int months) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                "DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m') as month",
                SYS_ORDER.PAYID.count().as("order_count"),
                SYS_ORDER.TOTAL_AMOUNT.sum().as("total_amount"),
                SYS_ORDER.COMMISSION_AMOUNT.sum().as("total_commission")
            )
            .from(SYS_ORDER)
            .where(SYS_ORDER.CREATE_TIME.ge(
                System.currentTimeMillis() / 1000 - months * 30L * 24 * 60 * 60
            ))
            .groupBy("month")
            .orderBy("month");
        
        return selectListByQueryAs(queryWrapper, Map.class);
    }
    
    /**
     * 查询异常订单（使用复杂条件）
     * 
     * @return 异常订单列表
     */
    default List<SysOrder> selectAbnormalOrders() {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.ALL_COLUMNS)
            .from(SYS_ORDER)
            .where(
                // 条件1：金额异常
                SYS_ORDER.TOTAL_AMOUNT.le(0)
                .or(SYS_ORDER.COMMISSION_AMOUNT.lt(0))
                .or(SYS_ORDER.ACTUAL_AMOUNT.lt(0))
            )
            .or(
                // 条件2：时间异常
                SYS_ORDER.START_TIME.ge(SYS_ORDER.END_TIME)
            )
            .or(
                // 条件3：状态异常
                SYS_ORDER.ORDER_STATUS.notIn(0, 1, 2)
            )
            .orderBy(SYS_ORDER.CREATE_TIME.desc());
        
        return selectListByQuery(queryWrapper);
    }
}
