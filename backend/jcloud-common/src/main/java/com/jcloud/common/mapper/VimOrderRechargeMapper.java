package com.jcloud.common.mapper;

import com.jcloud.common.entity.VimOrderRecharge;
import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * VimOrderRecharge Mapper接口（从库查询）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@UseDataSource("slave")
public interface VimOrderRechargeMapper extends BaseMapper<VimOrderRecharge> {
    
    /**
     * 按日期统计充值趋势数据
     *
     * @param startTimestamp 开始时间戳（秒）
     * @param endTimestamp 结束时间戳（秒）
     * @return 充值趋势统计数据
     */
    List<RechargeTrendStats> getRechargeTrendStats(
        @Param("startTimestamp") Integer startTimestamp,
        @Param("endTimestamp") Integer endTimestamp
    );

    /**
     * 查询指定用户在指定时间范围内的成功充值记录
     *
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<VimOrderRecharge> selectSuccessRechargesByUser(@Param("uid") Integer uid,
                                                       @Param("startTime") Integer startTime,
                                                       @Param("endTime") Integer endTime);
    
    /**
     * 查询指定用户列表在指定时间范围内的成功充值记录
     * 
     * @param uids 用户ID列表
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    List<VimOrderRecharge> selectSuccessRechargesByUsers(@Param("uids") List<Integer> uids,
                                                        @Param("startTime") Integer startTime,
                                                        @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户在指定时间范围内的充值总金额
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    BigDecimal sumRechargeAmountByUser(@Param("uid") Integer uid,
                                      @Param("startTime") Integer startTime,
                                      @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户列表在指定时间范围内的充值总金额
     * 
     * @param uids 用户ID列表
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值总金额
     */
    BigDecimal sumRechargeAmountByUsers(@Param("uids") List<Integer> uids,
                                       @Param("startTime") Integer startTime,
                                       @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户在指定时间范围内的充值次数
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值次数
     */
    @Select("SELECT COUNT(*) FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Long countRechargesByUser(@Param("uid") Integer uid,
                             @Param("startTime") Integer startTime,
                             @Param("endTime") Integer endTime);
    
    /**
     * 查询指定时间范围内的所有成功充值记录
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    @Select("SELECT * FROM vim_order_recharge WHERE state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<VimOrderRecharge> selectSuccessRechargesByTimeRange(@Param("startTime") Integer startTime,
                                                            @Param("endTime") Integer endTime);
    
    /**
     * 查询用户的最近一次成功充值记录
     * 
     * @param uid 用户ID
     * @return 最近充值记录
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 ORDER BY create_time DESC LIMIT 1")
    VimOrderRecharge selectLatestSuccessRechargeByUser(@Param("uid") Integer uid);
    
    /**
     * 查询指定用户的首次充值记录
     * 
     * @param uid 用户ID
     * @return 首次充值记录
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 ORDER BY create_time ASC LIMIT 1")
    VimOrderRecharge selectFirstRechargeByUser(@Param("uid") Integer uid);
    
    /**
     * 统计指定时间范围内的充值用户数
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值用户数
     */
    @Select("SELECT COUNT(DISTINCT uid) FROM vim_order_recharge WHERE state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Long countRechargeUsersByTimeRange(@Param("startTime") Integer startTime,
                                      @Param("endTime") Integer endTime);
    
    /**
     * 查询指定用户是否在指定时间范围内有充值记录
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 是否有充值记录
     */
    @Select("SELECT COUNT(*) > 0 FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    boolean existsRechargeByUserAndTimeRange(@Param("uid") Integer uid,
                                           @Param("startTime") Integer startTime,
                                           @Param("endTime") Integer endTime);

    /**
     * 查询用户充值行为统计数据
     *
     * @param startTimestamp 开始时间戳（秒）
     * @param endTimestamp 结束时间戳（秒）
     * @return 用户充值行为统计数据
     */
    List<UserBehaviorStats> getUserBehaviorStats(
        @Param("startTimestamp") Integer startTimestamp,
        @Param("endTimestamp") Integer endTimestamp
    );

    // ==================== 内部统计数据类 ====================

    /**
     * 充值趋势统计数据
     */
    class RechargeTrendStats {
        private String date;
        private Integer rechargeCount;
        private BigDecimal totalAmount;
        private BigDecimal avgAmount;

        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Integer getRechargeCount() { return rechargeCount; }
        public void setRechargeCount(Integer rechargeCount) { this.rechargeCount = rechargeCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public BigDecimal getAvgAmount() { return avgAmount; }
        public void setAvgAmount(BigDecimal avgAmount) { this.avgAmount = avgAmount; }
    }

    /**
     * 用户充值行为统计数据
     */
    static class UserBehaviorStats {
        private Integer userId;
        private String userName;
        private Integer rechargeCount;
        private BigDecimal totalAmount;
        private BigDecimal avgAmount;
        private Integer userLevel;
        private String lastRechargeTime;
        private String userType;
        private Integer activityScore;

        // getters and setters
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Integer getRechargeCount() { return rechargeCount; }
        public void setRechargeCount(Integer rechargeCount) { this.rechargeCount = rechargeCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public BigDecimal getAvgAmount() { return avgAmount; }
        public void setAvgAmount(BigDecimal avgAmount) { this.avgAmount = avgAmount; }
        public Integer getUserLevel() { return userLevel; }
        public void setUserLevel(Integer userLevel) { this.userLevel = userLevel; }
        public String getLastRechargeTime() { return lastRechargeTime; }
        public void setLastRechargeTime(String lastRechargeTime) { this.lastRechargeTime = lastRechargeTime; }
        public String getUserType() { return userType; }
        public void setUserType(String userType) { this.userType = userType; }
        public Integer getActivityScore() { return activityScore; }
        public void setActivityScore(Integer activityScore) { this.activityScore = activityScore; }
    }
}
