package com.jcloud.common.mapper;

import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 对战数据访问层（从库查询）
 */
@Mapper
@UseDataSource("slave")
public interface VimBattleMapper extends BaseMapper<Object> {

    /**
     * 按日期统计对战收入数据
     * 
     * @param startTimestamp 开始时间戳（秒）
     * @param endTimestamp 结束时间戳（秒）
     * @return 对战收入统计数据
     */
    List<BattleRevenueStats> getBattleRevenueByDate(
        @Param("startTimestamp") Integer startTimestamp,
        @Param("endTimestamp") Integer endTimestamp
    );

    // ==================== 内部统计数据类 ====================
    
    /**
     * 对战收入统计数据
     */
    class BattleRevenueStats {
        private String date;
        private Integer battleCount;
        private BigDecimal totalRevenue;
        private BigDecimal avgRevenue;
        
        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Integer getBattleCount() { return battleCount; }
        public void setBattleCount(Integer battleCount) { this.battleCount = battleCount; }
        public BigDecimal getTotalRevenue() { return totalRevenue; }
        public void setTotalRevenue(BigDecimal totalRevenue) { this.totalRevenue = totalRevenue; }
        public BigDecimal getAvgRevenue() { return avgRevenue; }
        public void setAvgRevenue(BigDecimal avgRevenue) { this.avgRevenue = avgRevenue; }
    }
}
