package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 批量结算请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "批量结算请求")
public class BatchSettleRequest {
    
    /**
     * 订单结算项列表
     */
    @Schema(description = "订单结算项列表")
    @NotEmpty(message = "结算项列表不能为空")
    @Valid
    private List<ActualSettlementItem> items;
}
