package com.jcloud.common.config;

// import com.jcloud.admin.config.JCloudDataPermissionDialect; // 暂时注释，稍后在admin模块中配置

import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.tenant.TenantFactory;
import com.mybatisflex.core.tenant.TenantManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 增强版MyBatis-Flex配置类
 * 集成完整的SQL审计功能，支持用户上下文、数据脱敏和多种存储方式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@Slf4j
public class EnhancedMyBatisFlexConfig {

    private static final Logger sqlLogger = LoggerFactory.getLogger("mybatis-flex-sql");

    public EnhancedMyBatisFlexConfig() {
        log.info("🚀 EnhancedMyBatisFlexConfig 配置类已加载！");
    }

    /**
     * MyBatis-Flex自定义配置
     * 启用增强的SQL审计功能、数据权限和多租户支持
     */
    @Bean
    @Primary
    public MyBatisFlexCustomizer enhancedMyBatisFlexCustomizer(SqlAuditProperties sqlAuditProperties) {
        return new MyBatisFlexCustomizer() {
            @Override
            public void customize(FlexGlobalConfig globalConfig) {
                log.info("开始配置MyBatis-Flex增强功能...");

                // 1. 配置SQL审计功能
                if (sqlAuditProperties.isEnabled()) {
                    AuditManager.setAuditEnable(true);
                    AuditManager.setMessageCollector(new ConsoleMessageCollector());
                    log.info("SQL审计功能已启用 - 慢SQL阈值: {}ms", sqlAuditProperties.getSlowSqlThreshold());
                }

                // 2. 配置数据权限方言（在admin模块中配置）
                // DialectFactory.registerDialect(DbType.MYSQL, new JCloudDataPermissionDialect());
                // log.info("数据权限方言已注册");

                // 3. 配置多租户支持
                configureTenantSupport();

                // 4. 配置全局租户列名
                globalConfig.setTenantColumn("tenant_id");
                log.info("全局租户列名已配置: tenant_id");

                log.info("MyBatis-Flex增强功能配置完成");
            }
        };
    }

    /**
     * 配置多租户支持
     */
    private void configureTenantSupport() {
        TenantManager.setTenantFactory(new TenantFactory() {
            @Override
            public Object[] getTenantIds() {
                try {
                    return resolveTenantIds();
                } catch (Exception e) {
                    log.warn("获取租户ID失败: {}", e.getMessage());
                    return new Object[0];
                }
            }
        });
        log.info("多租户工厂已配置");
    }

    /**
     * 解析租户ID
     * 拆分复杂的租户ID获取逻辑
     */
    private Object[] resolveTenantIds() {
        // 优先从SecurityUtils获取租户ID
        Long tenantId = getTenantIdFromSecurity();
        if (tenantId != null) {
            return new Object[]{tenantId};
        }

        // 备用方案：从RequestContextHolder获取
        Object requestTenantId = getTenantIdFromRequest();
        if (requestTenantId != null) {
            return new Object[]{requestTenantId};
        }

        // 如果都获取不到，返回空数组（不进行租户过滤）
        log.debug("未获取到租户ID，跳过租户过滤");
        return new Object[0];
    }

    /**
     * 从SecurityUtils获取租户ID
     */
    private Long getTenantIdFromSecurity() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId != null) {
            log.debug("从SecurityUtils获取租户ID: {}", tenantId);
        }
        return tenantId;
    }

    /**
     * 从RequestContextHolder获取租户ID
     */
    private Object getTenantIdFromRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            Object requestTenantId = attributes.getAttribute("tenantId", RequestAttributes.SCOPE_REQUEST);
            if (requestTenantId != null) {
                log.debug("从RequestContextHolder获取租户ID: {}", requestTenantId);
            }
            return requestTenantId;
        }
        return null;
    }

    // 暂时注释掉复杂的Bean定义，先测试基本功能
}
