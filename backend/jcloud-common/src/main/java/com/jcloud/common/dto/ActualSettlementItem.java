package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实际结算金额项DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "实际结算金额项")
public class ActualSettlementItem {
    
    /**
     * 订单ID
     */
    @Schema(description = "订单ID", example = "MAIN_1640995200_1234")
    @NotBlank(message = "订单ID不能为空")
    private String payid;
    
    /**
     * 订单编号显示（用于前端显示）
     */
    @Schema(description = "订单编号显示", example = "MAIN_1640995200_1234")
    private String orderDisplay;
    
    /**
     * 系统计算的推广劳务费
     */
    @Schema(description = "系统计算的推广劳务费", example = "100.00")
    @NotNull(message = "系统计算的推广劳务费不能为空")
    @DecimalMin(value = "0", message = "系统计算的推广劳务费不能为负数")
    private BigDecimal calculatedFee;
    
    /**
     * 实际结算金额
     */
    @Schema(description = "实际结算金额", example = "95.00")
    @NotNull(message = "实际结算金额不能为空")
    @DecimalMin(value = "0.01", message = "实际结算金额必须大于0")
    private BigDecimal actualAmount;
}
