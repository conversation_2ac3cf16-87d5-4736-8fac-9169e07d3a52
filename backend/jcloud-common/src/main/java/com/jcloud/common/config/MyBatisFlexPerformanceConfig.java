package com.jcloud.common.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.core.dialect.DbType;
import com.mybatisflex.core.dialect.DialectFactory;
import com.mybatisflex.core.dialect.impl.MySqlDialect;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex性能优化配置
 * 应用最佳实践，提升查询性能和开发效率
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class MyBatisFlexPerformanceConfig {
    
    /**
     * MyBatis-Flex性能优化配置
     */
    @Bean
    public MyBatisFlexCustomizer performanceCustomizer() {
        return new MyBatisFlexCustomizer() {
            @Override
            public void customize(FlexGlobalConfig globalConfig) {
                log.info("开始配置MyBatis-Flex性能优化...");
                
                // 1. 启用智能优化
                enableSmartOptimizations(globalConfig);
                
                // 2. 配置查询优化
                configureQueryOptimizations(globalConfig);
                
                // 3. 配置缓存优化
                configureCacheOptimizations(globalConfig);
                
                // 4. 配置SQL优化
                configureSqlOptimizations();
                
                // 5. 配置性能监控
                configurePerformanceMonitoring();
                
                log.info("MyBatis-Flex性能优化配置完成");
            }
        };
    }
    
    /**
     * 启用智能优化功能
     */
    private void enableSmartOptimizations(FlexGlobalConfig globalConfig) {
        // 启用智能IN转换为等号查询（当IN条件只有一个值时）
        QueryWrapper.setSmartConvertInToEquals(true);
        log.info("已启用智能IN转换优化");
        
        // 启用空值忽略（自动忽略null条件）
        globalConfig.setIgnoreNullValueWhenUpdate(true);
        log.info("已启用空值忽略优化");
        
        // 启用逻辑删除
        globalConfig.setLogicDeleteColumn("deleted");
        globalConfig.setLogicDeleteValue("1");
        globalConfig.setLogicNormalValue("0");
        log.info("已配置逻辑删除");
    }
    
    /**
     * 配置查询优化
     */
    private void configureQueryOptimizations(FlexGlobalConfig globalConfig) {
        // 设置默认查询限制（防止大量数据查询）
        globalConfig.setDefaultSelectLimit(10000);
        log.info("已设置默认查询限制: 10000");
        
        // 启用分页优化
        globalConfig.setOptimizeJoin(true);
        log.info("已启用JOIN优化");
        
        // 配置批量操作大小
        globalConfig.setDefaultBatchSize(1000);
        log.info("已设置默认批量操作大小: 1000");
    }
    
    /**
     * 配置缓存优化
     */
    private void configureCacheOptimizations(FlexGlobalConfig globalConfig) {
        // 启用一级缓存
        globalConfig.setLocalCacheScope("SESSION");
        log.info("已启用一级缓存");
        
        // 配置懒加载
        globalConfig.setLazyLoadingEnabled(true);
        globalConfig.setAggressiveLazyLoading(false);
        log.info("已配置懒加载");
    }
    
    /**
     * 配置SQL优化
     */
    private void configureSqlOptimizations() {
        // 注册优化的MySQL方言
        DialectFactory.registerDialect(DbType.MYSQL, new OptimizedMySqlDialect());
        log.info("已注册优化的MySQL方言");
    }
    
    /**
     * 配置性能监控
     */
    private void configurePerformanceMonitoring() {
        // 配置SQL审计
        AuditManager.setAuditEnable(true);
        AuditManager.setMessageCollector(new PerformanceMessageCollector());
        log.info("已配置性能监控");
    }
    
    /**
     * 优化的MySQL方言
     */
    private static class OptimizedMySqlDialect extends MySqlDialect {
        
        @Override
        public String forSelectByQuery(QueryWrapper queryWrapper) {
            String sql = super.forSelectByQuery(queryWrapper);
            
            // 添加查询提示（如果需要）
            if (needsOptimization(queryWrapper)) {
                sql = addQueryHints(sql);
            }
            
            return sql;
        }
        
        /**
         * 判断是否需要优化
         */
        private boolean needsOptimization(QueryWrapper queryWrapper) {
            // 检查是否有复杂的JOIN或子查询
            return queryWrapper.getJoinTables() != null && !queryWrapper.getJoinTables().isEmpty();
        }
        
        /**
         * 添加查询提示
         */
        private String addQueryHints(String sql) {
            // 添加MySQL查询提示
            if (sql.toUpperCase().contains("JOIN")) {
                // 对于JOIN查询，建议使用BNL算法
                sql = sql.replaceFirst("SELECT", "SELECT /*+ USE_BNL() */");
            }
            
            return sql;
        }
    }
    
    /**
     * 性能监控消息收集器
     */
    private static class PerformanceMessageCollector implements MessageCollector {
        
        private static final long SLOW_QUERY_THRESHOLD = 1000; // 1秒
        
        @Override
        public void collect(String sql, Object[] args, long executionTime) {
            if (executionTime > SLOW_QUERY_THRESHOLD) {
                log.warn("慢查询检测 - 执行时间: {}ms, SQL: {}", executionTime, sql);
                
                // 分析慢查询原因
                analyzSlowQuery(sql, executionTime);
            } else {
                log.debug("SQL执行 - 执行时间: {}ms, SQL: {}", executionTime, sql);
            }
        }
        
        /**
         * 分析慢查询
         */
        private void analyzSlowQuery(String sql, long executionTime) {
            StringBuilder analysis = new StringBuilder();
            analysis.append("慢查询分析: ");
            
            // 检查是否缺少索引
            if (sql.toUpperCase().contains("WHERE") && !sql.toUpperCase().contains("INDEX")) {
                analysis.append("[可能缺少索引] ");
            }
            
            // 检查是否有全表扫描
            if (sql.toUpperCase().contains("SELECT *")) {
                analysis.append("[避免SELECT *] ");
            }
            
            // 检查是否有复杂的JOIN
            if (sql.toUpperCase().contains("JOIN")) {
                long joinCount = sql.toUpperCase().chars()
                    .mapToObj(c -> (char) c)
                    .map(String::valueOf)
                    .reduce("", String::concat)
                    .split("JOIN").length - 1;
                if (joinCount > 3) {
                    analysis.append("[JOIN过多: ").append(joinCount).append("] ");
                }
            }
            
            // 检查是否有子查询
            if (sql.toUpperCase().contains("SELECT") && 
                sql.toUpperCase().indexOf("SELECT") != sql.toUpperCase().lastIndexOf("SELECT")) {
                analysis.append("[包含子查询] ");
            }
            
            log.warn("{} - 执行时间: {}ms", analysis.toString(), executionTime);
        }
    }
    
    /**
     * QueryWrapper性能优化工具类
     */
    public static class QueryWrapperOptimizer {
        
        /**
         * 优化分页查询
         */
        public static QueryWrapper optimizePagination(QueryWrapper queryWrapper, int pageSize) {
            // 对于大分页，使用游标分页
            if (pageSize > 1000) {
                log.warn("大分页查询检测，建议使用游标分页");
            }
            
            // 确保有排序条件
            if (queryWrapper.getOrderBys().isEmpty()) {
                log.warn("分页查询缺少排序条件，可能导致结果不稳定");
            }
            
            return queryWrapper;
        }
        
        /**
         * 优化JOIN查询
         */
        public static QueryWrapper optimizeJoin(QueryWrapper queryWrapper) {
            // 检查JOIN条件
            if (queryWrapper.getJoinTables() != null && !queryWrapper.getJoinTables().isEmpty()) {
                log.debug("JOIN查询检测，确保JOIN条件使用索引");
            }
            
            return queryWrapper;
        }
        
        /**
         * 优化IN查询
         */
        public static QueryWrapper optimizeInQuery(QueryWrapper queryWrapper) {
            // IN条件过多时建议使用临时表
            // 这里只是示例，实际需要根据具体情况处理
            return queryWrapper;
        }
        
        /**
         * 添加查询提示
         */
        public static QueryWrapper addHints(QueryWrapper queryWrapper, String... hints) {
            for (String hint : hints) {
                // 添加查询提示的逻辑
                log.debug("添加查询提示: {}", hint);
            }
            return queryWrapper;
        }
    }
    
    /**
     * 性能监控指标
     */
    public static class PerformanceMetrics {
        
        private static long totalQueries = 0;
        private static long slowQueries = 0;
        private static long totalExecutionTime = 0;
        
        public static void recordQuery(long executionTime) {
            totalQueries++;
            totalExecutionTime += executionTime;
            
            if (executionTime > 1000) {
                slowQueries++;
            }
        }
        
        public static double getAverageExecutionTime() {
            return totalQueries > 0 ? (double) totalExecutionTime / totalQueries : 0;
        }
        
        public static double getSlowQueryRate() {
            return totalQueries > 0 ? (double) slowQueries / totalQueries * 100 : 0;
        }
        
        public static void logMetrics() {
            log.info("性能指标 - 总查询数: {}, 慢查询数: {}, 慢查询率: {:.2f}%, 平均执行时间: {:.2f}ms",
                totalQueries, slowQueries, getSlowQueryRate(), getAverageExecutionTime());
        }
    }
}
