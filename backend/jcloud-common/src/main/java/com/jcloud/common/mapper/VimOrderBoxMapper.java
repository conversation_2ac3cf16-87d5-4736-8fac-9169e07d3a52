package com.jcloud.common.mapper;

import com.jcloud.common.entity.VimOrderBox;
import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * VimOrderBox Mapper接口（从库查询）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@UseDataSource("slave")
public interface VimOrderBoxMapper extends BaseMapper<VimOrderBox> {
    
    /**
     * 按日期和业务类型统计收入数据
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 业务收入统计数据
     */
    List<BusinessRevenueStats> getBusinessRevenueByDateAndType(
        @Param("startTimestamp") String startTimestamp,
        @Param("endTimestamp") String endTimestamp
    );
    
    /**
     * 按业务类型统计总收入
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 业务类型收入统计
     */
    List<BusinessTypeStats> getBusinessTypeStats(
        @Param("startTimestamp") String startTimestamp,
        @Param("endTimestamp") String endTimestamp
    );
    
    /**
     * 查询指定时间范围内的开箱消费数据
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 开箱消费统计数据
     */
    List<BoxConsumptionStats> getBoxConsumptionStats(
        @Param("startTimestamp") String startTimestamp,
        @Param("endTimestamp") String endTimestamp
    );
    

    
    /**
     * 查询用户行为散点图数据
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 用户行为数据
     */
    List<UserBehaviorStats> getUserBehaviorStats(
        @Param("startTimestamp") String startTimestamp,
        @Param("endTimestamp") String endTimestamp
    );
    

    
    /**
     * 查询用户活跃度热力图数据
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 用户活跃度数据
     */
    List<UserActivityStats> getUserActivityStats(
        @Param("startTimestamp") String startTimestamp,
        @Param("endTimestamp") String endTimestamp
    );
    
    // ==================== 内部统计数据类 ====================
    
    /**
     * 业务收入统计数据
     */
    class BusinessRevenueStats {
        private String date;
        private Integer businessType;
        private Integer orderCount;
        private BigDecimal totalAmount;
        private BigDecimal avgAmount;
        
        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Integer getBusinessType() { return businessType; }
        public void setBusinessType(Integer businessType) { this.businessType = businessType; }
        public Integer getOrderCount() { return orderCount; }
        public void setOrderCount(Integer orderCount) { this.orderCount = orderCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public BigDecimal getAvgAmount() { return avgAmount; }
        public void setAvgAmount(BigDecimal avgAmount) { this.avgAmount = avgAmount; }
    }
    
    /**
     * 业务类型统计数据
     */
    class BusinessTypeStats {
        private Integer businessType;
        private String businessTypeName;
        private Integer orderCount;
        private BigDecimal totalAmount;
        private BigDecimal percentage;
        
        // getters and setters
        public Integer getBusinessType() { return businessType; }
        public void setBusinessType(Integer businessType) { this.businessType = businessType; }
        public String getBusinessTypeName() { return businessTypeName; }
        public void setBusinessTypeName(String businessTypeName) { this.businessTypeName = businessTypeName; }
        public Integer getOrderCount() { return orderCount; }
        public void setOrderCount(Integer orderCount) { this.orderCount = orderCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public BigDecimal getPercentage() { return percentage; }
        public void setPercentage(BigDecimal percentage) { this.percentage = percentage; }
    }
    
    /**
     * 开箱消费统计数据
     */
    class BoxConsumptionStats {
        private String date;
        private Integer boxCount;
        private BigDecimal boxAmount;
        private BigDecimal itemValue;
        private Integer userCount;
        
        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Integer getBoxCount() { return boxCount; }
        public void setBoxCount(Integer boxCount) { this.boxCount = boxCount; }
        public BigDecimal getBoxAmount() { return boxAmount; }
        public void setBoxAmount(BigDecimal boxAmount) { this.boxAmount = boxAmount; }
        public BigDecimal getItemValue() { return itemValue; }
        public void setItemValue(BigDecimal itemValue) { this.itemValue = itemValue; }
        public Integer getUserCount() { return userCount; }
        public void setUserCount(Integer userCount) { this.userCount = userCount; }
    }
    

    
    /**
     * 用户行为统计数据
     */
    class UserBehaviorStats {
        private Integer uid;
        private Integer totalOrders;
        private BigDecimal totalSpent;
        private BigDecimal avgOrderValue;
        private String userType;
        private Integer userLevel;
        
        // getters and setters
        public Integer getUid() { return uid; }
        public void setUid(Integer uid) { this.uid = uid; }
        public Integer getTotalOrders() { return totalOrders; }
        public void setTotalOrders(Integer totalOrders) { this.totalOrders = totalOrders; }
        public BigDecimal getTotalSpent() { return totalSpent; }
        public void setTotalSpent(BigDecimal totalSpent) { this.totalSpent = totalSpent; }
        public BigDecimal getAvgOrderValue() { return avgOrderValue; }
        public void setAvgOrderValue(BigDecimal avgOrderValue) { this.avgOrderValue = avgOrderValue; }
        public String getUserType() { return userType; }
        public void setUserType(String userType) { this.userType = userType; }
        public Integer getUserLevel() { return userLevel; }
        public void setUserLevel(Integer userLevel) { this.userLevel = userLevel; }
    }
    

    
    /**
     * 用户活跃度统计数据
     */
    class UserActivityStats {
        private String date;
        private Integer hour;
        private Integer activeUsers;
        private BigDecimal transactionAmount;
        
        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Integer getHour() { return hour; }
        public void setHour(Integer hour) { this.hour = hour; }
        public Integer getActiveUsers() { return activeUsers; }
        public void setActiveUsers(Integer activeUsers) { this.activeUsers = activeUsers; }
        public BigDecimal getTransactionAmount() { return transactionAmount; }
        public void setTransactionAmount(BigDecimal transactionAmount) { this.transactionAmount = transactionAmount; }
    }
}
