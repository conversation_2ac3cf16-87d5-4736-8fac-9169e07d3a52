package com.jcloud.common.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Vim开箱订单实体类（从库查询）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table(value = "vim_order_box", dataSource = "slave")
@Schema(description = "Vim开箱订单")
public class VimOrderBox {
    
    /**
     * 订单ID
     */
    @Id(keyType = KeyType.None)
    @Schema(description = "订单ID")
    private String id;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer uid;
    
    /**
     * 业务类型
     * 1：开箱 2：兑换 3：对战 5：升级 6：补偿 7：Roll房 8：奖励 9：融合
     */
    @Schema(description = "业务类型")
    private Integer type;
    
    /**
     * 订单金额
     */
    @Schema(description = "订单金额")
    private BigDecimal price;
    
    /**
     * 箱子ID
     */
    @Schema(description = "箱子ID")
    private Integer boxId;
    
    /**
     * 箱子名称
     */
    @Schema(description = "箱子名称")
    private String boxName;
    
    /**
     * 获得物品ID
     */
    @Schema(description = "获得物品ID")
    private Integer itemId;
    
    /**
     * 获得物品名称
     */
    @Schema(description = "获得物品名称")
    private String itemName;
    
    /**
     * 物品价值
     */
    @Schema(description = "物品价值")
    private BigDecimal itemValue;
    
    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private Integer state;
    
    /**
     * 创建时间（时间戳，毫秒）
     */
    @Schema(description = "创建时间")
    private String timestamp;
    
    /**
     * 更新时间（时间戳）
     */
    @Schema(description = "更新时间")
    private Integer updateTime;
    
    // ==================== 业务方法 ====================
    
    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        if (type == null) return "未知";
        
        switch (type) {
            case 1: return "开箱";
            case 2: return "兑换";
            case 3: return "对战";
            case 5: return "升级";
            case 6: return "补偿";
            case 7: return "Roll房";
            case 8: return "奖励";
            case 9: return "融合";
            default: return "其他";
        }
    }
    
    /**
     * 获取订单金额（非空安全）
     */
    public BigDecimal getSafePrice() {
        return price != null ? price : BigDecimal.ZERO;
    }
    
    /**
     * 获取物品价值（非空安全）
     */
    public BigDecimal getSafeItemValue() {
        return itemValue != null ? itemValue : BigDecimal.ZERO;
    }
    
    /**
     * 计算盈亏比例
     */
    public BigDecimal getProfitRatio() {
        if (price == null || price.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal safeItemValue = getSafeItemValue();
        return safeItemValue.divide(price, 4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 判断是否盈利
     */
    public boolean isProfitable() {
        return getProfitRatio().compareTo(BigDecimal.ONE) > 0;
    }
    
    /**
     * 获取时间戳（长整型）
     */
    public Long getTimestampAsLong() {
        if (timestamp == null) return null;
        try {
            return Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
