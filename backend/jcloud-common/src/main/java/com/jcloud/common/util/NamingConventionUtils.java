package com.jcloud.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * 命名规范工具类
 * 提供命名规范检查和转换功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class NamingConventionUtils {
    
    // 小驼峰命名正则表达式（变量名、方法名）
    private static final Pattern CAMEL_CASE_PATTERN = Pattern.compile("^[a-z][a-zA-Z0-9]*$");
    
    // 大驼峰命名正则表达式（类名）
    private static final Pattern PASCAL_CASE_PATTERN = Pattern.compile("^[A-Z][a-zA-Z0-9]*$");
    
    // 常量命名正则表达式（全大写下划线）
    private static final Pattern CONSTANT_CASE_PATTERN = Pattern.compile("^[A-Z][A-Z0-9_]*$");
    
    // 包名命名正则表达式（全小写点分隔）
    private static final Pattern PACKAGE_NAME_PATTERN = Pattern.compile("^[a-z][a-z0-9]*(\\.[a-z][a-z0-9]*)*$");
    
    /**
     * 检查是否符合小驼峰命名规范（变量名、方法名）
     * 
     * @param name 名称
     * @return 是否符合规范
     */
    public static boolean isCamelCase(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return CAMEL_CASE_PATTERN.matcher(name).matches();
    }
    
    /**
     * 检查是否符合大驼峰命名规范（类名）
     * 
     * @param name 名称
     * @return 是否符合规范
     */
    public static boolean isPascalCase(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return PASCAL_CASE_PATTERN.matcher(name).matches();
    }
    
    /**
     * 检查是否符合常量命名规范（全大写下划线）
     * 
     * @param name 名称
     * @return 是否符合规范
     */
    public static boolean isConstantCase(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return CONSTANT_CASE_PATTERN.matcher(name).matches();
    }
    
    /**
     * 检查是否符合包名命名规范
     * 
     * @param packageName 包名
     * @return 是否符合规范
     */
    public static boolean isValidPackageName(String packageName) {
        if (packageName == null || packageName.trim().isEmpty()) {
            return false;
        }
        return PACKAGE_NAME_PATTERN.matcher(packageName).matches();
    }
    
    /**
     * 将字符串转换为小驼峰命名
     * 
     * @param input 输入字符串
     * @return 小驼峰命名字符串
     */
    public static String toCamelCase(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }
        
        // 处理下划线分隔的字符串
        if (input.contains("_")) {
            return convertUnderscoreToCamelCase(input, false);
        }
        
        // 处理连字符分隔的字符串
        if (input.contains("-")) {
            return convertHyphenToCamelCase(input, false);
        }
        
        // 如果已经是驼峰命名，确保首字母小写
        return Character.toLowerCase(input.charAt(0)) + input.substring(1);
    }
    
    /**
     * 将字符串转换为大驼峰命名
     * 
     * @param input 输入字符串
     * @return 大驼峰命名字符串
     */
    public static String toPascalCase(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }
        
        // 处理下划线分隔的字符串
        if (input.contains("_")) {
            return convertUnderscoreToCamelCase(input, true);
        }
        
        // 处理连字符分隔的字符串
        if (input.contains("-")) {
            return convertHyphenToCamelCase(input, true);
        }
        
        // 如果已经是驼峰命名，确保首字母大写
        return Character.toUpperCase(input.charAt(0)) + input.substring(1);
    }
    
    /**
     * 将字符串转换为常量命名（全大写下划线）
     * 
     * @param input 输入字符串
     * @return 常量命名字符串
     */
    public static String toConstantCase(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }
        
        StringBuilder result = new StringBuilder();
        boolean previousWasLowerCase = false;
        
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            
            if (Character.isUpperCase(c) && previousWasLowerCase) {
                result.append('_');
            }
            
            if (c == '-' || c == ' ') {
                result.append('_');
            } else {
                result.append(Character.toUpperCase(c));
            }
            
            previousWasLowerCase = Character.isLowerCase(c);
        }
        
        return result.toString();
    }
    
    /**
     * 将下划线分隔的字符串转换为驼峰命名
     * 
     * @param input 输入字符串
     * @param pascalCase 是否为大驼峰（true）还是小驼峰（false）
     * @return 驼峰命名字符串
     */
    private static String convertUnderscoreToCamelCase(String input, boolean pascalCase) {
        String[] parts = input.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (part.isEmpty()) {
                continue;
            }
            
            if (i == 0 && !pascalCase) {
                result.append(part);
            } else {
                result.append(Character.toUpperCase(part.charAt(0)));
                if (part.length() > 1) {
                    result.append(part.substring(1));
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 将连字符分隔的字符串转换为驼峰命名
     * 
     * @param input 输入字符串
     * @param pascalCase 是否为大驼峰（true）还是小驼峰（false）
     * @return 驼峰命名字符串
     */
    private static String convertHyphenToCamelCase(String input, boolean pascalCase) {
        String[] parts = input.toLowerCase().split("-");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (part.isEmpty()) {
                continue;
            }
            
            if (i == 0 && !pascalCase) {
                result.append(part);
            } else {
                result.append(Character.toUpperCase(part.charAt(0)));
                if (part.length() > 1) {
                    result.append(part.substring(1));
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 验证方法名是否符合规范
     * 
     * @param methodName 方法名
     * @return 验证结果
     */
    public static ValidationResult validateMethodName(String methodName) {
        if (!isCamelCase(methodName)) {
            return ValidationResult.invalid("方法名应使用小驼峰命名法", toCamelCase(methodName));
        }
        return ValidationResult.valid();
    }
    
    /**
     * 验证类名是否符合规范
     * 
     * @param className 类名
     * @return 验证结果
     */
    public static ValidationResult validateClassName(String className) {
        if (!isPascalCase(className)) {
            return ValidationResult.invalid("类名应使用大驼峰命名法", toPascalCase(className));
        }
        return ValidationResult.valid();
    }
    
    /**
     * 验证常量名是否符合规范
     * 
     * @param constantName 常量名
     * @return 验证结果
     */
    public static ValidationResult validateConstantName(String constantName) {
        if (!isConstantCase(constantName)) {
            return ValidationResult.invalid("常量名应使用全大写下划线命名法", toConstantCase(constantName));
        }
        return ValidationResult.valid();
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        private final String suggestion;
        
        private ValidationResult(boolean valid, String message, String suggestion) {
            this.valid = valid;
            this.message = message;
            this.suggestion = suggestion;
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, null, null);
        }
        
        public static ValidationResult invalid(String message, String suggestion) {
            return new ValidationResult(false, message, suggestion);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getSuggestion() {
            return suggestion;
        }
        
        @Override
        public String toString() {
            if (valid) {
                return "命名规范验证通过";
            } else {
                return String.format("命名规范验证失败: %s，建议使用: %s", message, suggestion);
            }
        }
    }
}
