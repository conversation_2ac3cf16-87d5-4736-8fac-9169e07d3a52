# 公共配置文件
# 包含开发环境和生产环境共同的配置项

# Knife4j API文档配置
knife4j:
  enable: true
  openapi:
    title: jCloud权限管理系统API文档
    description: 企业级权限管理系统接口文档
    version: 1.0.0
    concat: <EMAIL>
    license: MIT
    license-url: https://opensource.org/licenses/MIT
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# SQL审计基础配置
jcloud:
  sql-audit:
    enabled: true
    slow-sql-threshold: 1000
    data-masking:
      sensitive-fields:
        - password
        - pwd
        - passwd
        - phone
        - mobile
        - email
        - idcard
        - identity
        - bankcard
        - credit_card
    performance:
      monitor-enabled: true
      buffer-size: 1000
      max-wait-time: 5000

# MyBatis-Flex配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    default-executor-type: simple
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    safe-result-handler-enabled: true
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-prefix: 
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    proxy-factory: javassist
    variables-enabled: true
    shrink-whitespaces-in-sql: false
    nullable-on-for-each: false
    arg-name-based-constructor-auto-mapping: false

# 分页配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 文件上传配置
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      enabled: true

# 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

# Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
      write-null-map-values: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# 线程池配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 20
        queue-capacity: 200
        keep-alive: 60s
      thread-name-prefix: jcloud-task-
    scheduling:
      pool:
        size: 5
      thread-name-prefix: jcloud-scheduling-

# 健康检查配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    diskspace:
      threshold: 10GB

# 服务器配置
server:
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true
