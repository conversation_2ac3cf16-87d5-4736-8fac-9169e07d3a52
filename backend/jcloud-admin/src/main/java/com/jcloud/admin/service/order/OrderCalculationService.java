package com.jcloud.admin.service.order;

import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.entity.VimUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Random;

/**
 * 订单计算服务
 * 负责订单相关的计算逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCalculationService {
    
    private final OperationsMapper operationsMapper;
    
    /**
     * 计算用户自充值金额
     * 
     * @param userId 用户ID
     * @param request 订单创建请求
     * @return 自充值金额
     */
    public BigDecimal calculateUserSelfRechargeAmount(Integer userId, OrderCreateRequest request) {
        Integer startTime = request.getStartTime().intValue();
        Integer endTime = request.getEndTime().intValue();
        
        try {
            // 使用OperationsMapper获取用户自充值金额
            BigDecimal selfRechargeAmount = operationsMapper.getUserSelfRechargeAmount(
                userId, startTime, endTime);
            
            log.debug("用户 {} 在时间段 [{}, {}] 的自充值金额: {}", 
                userId, startTime, endTime, selfRechargeAmount);
            
            return selfRechargeAmount != null ? selfRechargeAmount : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("计算用户自充值金额失败: userId={}, startTime={}, endTime={}", 
                userId, startTime, endTime, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 计算佣金
     * 
     * @param amount 金额
     * @param feeRate 费率
     * @return 佣金金额
     */
    public BigDecimal calculateCommission(BigDecimal amount, BigDecimal feeRate) {
        if (amount == null || feeRate == null) {
            return BigDecimal.ZERO;
        }
        
        // 佣金 = 金额 × 费率，保留2位小数
        return amount.multiply(feeRate)
                    .setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算代理佣金
     * 
     * @param totalAmount 总金额
     * @param agent 代理用户
     * @return 代理佣金
     */
    public BigDecimal calculateAgentCommission(BigDecimal totalAmount, VimUser agent) {
        if (totalAmount == null || agent == null) {
            return BigDecimal.ZERO;
        }
        
        // 获取代理费率，默认为0
        BigDecimal agentFeeRate = agent.getFeeRate() != null ? 
            agent.getFeeRate() : BigDecimal.ZERO;
        
        return calculateCommission(totalAmount, agentFeeRate);
    }
    
    /**
     * 计算主播佣金
     * 
     * @param totalAmount 总金额
     * @param anchor 主播用户
     * @return 主播佣金
     */
    public BigDecimal calculateAnchorCommission(BigDecimal totalAmount, VimUser anchor) {
        if (totalAmount == null || anchor == null) {
            return BigDecimal.ZERO;
        }
        
        // 获取主播费率，默认为0
        BigDecimal anchorFeeRate = anchor.getFeeRate() != null ? 
            anchor.getFeeRate() : BigDecimal.ZERO;
        
        return calculateCommission(totalAmount, anchorFeeRate);
    }
    
    /**
     * 计算订单总金额
     * 
     * @param baseAmount 基础金额
     * @param additionalAmount 附加金额
     * @return 总金额
     */
    public BigDecimal calculateTotalAmount(BigDecimal baseAmount, BigDecimal additionalAmount) {
        BigDecimal base = baseAmount != null ? baseAmount : BigDecimal.ZERO;
        BigDecimal additional = additionalAmount != null ? additionalAmount : BigDecimal.ZERO;
        
        return base.add(additional).setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算实际结算金额
     * 
     * @param originalAmount 原始金额
     * @param adjustmentAmount 调整金额
     * @return 实际结算金额
     */
    public BigDecimal calculateActualSettlementAmount(BigDecimal originalAmount, BigDecimal adjustmentAmount) {
        BigDecimal original = originalAmount != null ? originalAmount : BigDecimal.ZERO;
        BigDecimal adjustment = adjustmentAmount != null ? adjustmentAmount : BigDecimal.ZERO;
        
        BigDecimal result = original.add(adjustment);
        
        // 确保结算金额不为负数
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("计算出的实际结算金额为负数: {}, 调整为0", result);
            return BigDecimal.ZERO;
        }
        
        return result.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 生成订单ID
     * 
     * @param prefix 前缀
     * @return 订单ID
     */
    public String generateOrderId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" +
                String.format("%04d", new Random().nextInt(10000));
    }
    
    /**
     * 计算费率百分比显示
     * 
     * @param feeRate 费率（小数形式）
     * @return 百分比字符串
     */
    public String formatFeeRateAsPercentage(BigDecimal feeRate) {
        if (feeRate == null) {
            return "0%";
        }
        
        // 将小数形式的费率转换为百分比
        BigDecimal percentage = feeRate.multiply(new BigDecimal("100"))
                                      .setScale(2, RoundingMode.HALF_UP);
        return percentage + "%";
    }
    
    /**
     * 验证金额是否有效
     * 
     * @param amount 金额
     * @return 是否有效
     */
    public boolean isValidAmount(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) >= 0;
    }
    
    /**
     * 验证费率是否有效
     * 
     * @param feeRate 费率
     * @return 是否有效
     */
    public boolean isValidFeeRate(BigDecimal feeRate) {
        return feeRate != null && 
               feeRate.compareTo(BigDecimal.ZERO) >= 0 && 
               feeRate.compareTo(BigDecimal.ONE) <= 0;
    }
}
