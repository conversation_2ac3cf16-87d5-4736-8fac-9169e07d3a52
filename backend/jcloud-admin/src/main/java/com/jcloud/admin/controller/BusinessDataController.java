package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.dto.BusinessRevenueData;
import com.jcloud.admin.dto.CommissionData;
import com.jcloud.admin.dto.RechargeData;
import com.jcloud.admin.service.BusinessDataService;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.mapper.VimOrderRechargeMapper;
import com.jcloud.common.mapper.VimOrderBoxMapper;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务数据控制器
 * 提供基于真实数据库的业务数据查询API
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "业务数据管理", description = "业务数据查询相关接口")
@RestController
@RequestMapping("/api/business")
@RequiredArgsConstructor
@Slf4j
public class BusinessDataController {
    
    private final BusinessDataService businessDataService;
    
    @Operation(summary = "获取业务收入趋势数据", description = "基于vim_order_box表获取各业务类型的收入趋势")
    // @SaCheckPermission("financial:stats:query")  // 暂时移除权限验证
    @PostMapping("/revenue-trend")
    public Result<List<BusinessRevenueData>> getBusinessRevenueTrend(
            @Valid @RequestBody FinancialStatsRequest request) {
        
        log.info("开始查询业务收入趋势数据，参数：{}", request);
        
        try {
            List<BusinessRevenueData> data = businessDataService.getBusinessRevenueTrend(request);
            log.info("业务收入趋势数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取业务收入趋势数据成功", data);
        } catch (Exception e) {
            log.error("查询业务收入趋势数据失败", e);
            return Result.error("查询业务收入趋势数据失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "获取充值趋势数据", description = "基于vim_order_recharge表获取充值趋势")
    // @SaCheckPermission("financial:stats:query")  // 暂时移除权限验证
    @PostMapping("/recharge-trend")
    public Result<List<RechargeData>> getRechargeTrend(
            @Valid @RequestBody FinancialStatsRequest request) {
        
        log.info("开始查询充值趋势数据，参数：{}", request);
        
        try {
            List<RechargeData> data = businessDataService.getRechargeTrend(request);
            log.info("充值趋势数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取充值趋势数据成功", data);
        } catch (Exception e) {
            log.error("查询充值趋势数据失败", e);
            return Result.error("查询充值趋势数据失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "获取佣金趋势数据", description = "基于sys_order表获取佣金趋势")
    @SaCheckPermission("financial:stats:query")
    @PostMapping("/commission-trend")
    public Result<List<CommissionData>> getCommissionTrend(
            @Valid @RequestBody FinancialStatsRequest request) {
        
        log.info("开始查询佣金趋势数据，参数：{}", request);
        
        try {
            List<CommissionData> data = businessDataService.getCommissionTrend(request);
            log.info("佣金趋势数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取佣金趋势数据成功", data);
        } catch (Exception e) {
            log.error("查询佣金趋势数据失败", e);
            return Result.error("查询佣金趋势数据失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户活跃度热力图数据", description = "获取用户活跃度按时间分布的热力图数据")
    @SaCheckPermission("financial:stats:query")
    @PostMapping("/user/activity-heatmap")
    public Result<List<VimOrderBoxMapper.UserActivityStats>> getUserActivityHeatmap(
            @Valid @RequestBody FinancialStatsRequest request) {
        
        log.info("开始查询用户活跃度热力图数据，参数：{}", request);
        
        try {
            List<VimOrderBoxMapper.UserActivityStats> data = businessDataService.getUserActivityHeatmap(request);
            log.info("用户活跃度数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取用户活跃度数据成功", data);
        } catch (Exception e) {
            log.error("查询用户活跃度数据失败", e);
            return Result.error("查询用户活跃度数据失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "获取开箱消费趋势数据", description = "获取开箱业务的消费趋势数据")
    @SaCheckPermission("financial:stats:query")
    @PostMapping("/box/consumption-trend")
    public Result<List<VimOrderBoxMapper.BoxConsumptionStats>> getBoxConsumptionTrend(
            @Valid @RequestBody FinancialStatsRequest request) {
        log.info("开始查询开箱消费趋势数据，参数：{}", request);
        try {
            List<VimOrderBoxMapper.BoxConsumptionStats> data = businessDataService.getBoxConsumptionTrend(request);
            log.info("开箱消费数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取开箱消费数据成功", data);
        } catch (Exception e) {
            log.error("查询开箱消费数据失败", e);
            return Result.error("查询开箱消费数据失败：" + e.getMessage());
        }
    }
    @Operation(summary = "获取充值分布数据", description = "获取用户充值金额分布统计数据")
    @SaCheckPermission("financial:stats:query")
    @GetMapping("/recharge/distribution")
    public Result<List<Object>> getRechargeDistribution() {

        log.info("开始查询充值分布数据");

        try {
            List<Object> data = businessDataService.getRechargeDistribution();
            log.info("充值分布数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取充值分布数据成功", data);
        } catch (Exception e) {
            log.error("查询充值分布数据失败", e);
            return Result.error("查询充值分布数据失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取用户充值行为统计数据", description = "获取用户充值行为分析数据")
    @SaCheckPermission("financial:stats:query")
    @PostMapping("/user/behavior-stats")
    public Result<List<VimOrderRechargeMapper.UserBehaviorStats>> getUserBehaviorStats(
            @Valid @RequestBody FinancialStatsRequest request) {

        log.info("开始查询用户充值行为统计数据，参数：{}", request);

        try {
            List<VimOrderRechargeMapper.UserBehaviorStats> data = businessDataService.getUserBehaviorStats(request);
            log.info("用户充值行为数据查询成功，返回 {} 条记录", data.size());
            return Result.success("获取用户充值行为数据成功", data);
        } catch (Exception e) {
            log.error("查询用户充值行为数据失败", e);
            return Result.error("查询用户充值行为数据失败：" + e.getMessage());
        }
    }

}
