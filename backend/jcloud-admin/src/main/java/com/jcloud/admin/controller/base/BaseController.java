package com.jcloud.admin.controller.base;

import com.jcloud.common.result.Result;
import com.jcloud.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.function.Supplier;

/**
 * Controller基类
 * 提供通用的响应处理、异常处理和工具方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class BaseController {
    
    /**
     * 执行业务操作并处理异常
     * 
     * @param operation 业务操作
     * @param successMessage 成功消息
     * @param errorMessage 错误消息前缀
     * @param <T> 返回数据类型
     * @return 统一响应结果
     */
    protected <T> Result<T> executeOperation(Supplier<T> operation, String successMessage, String errorMessage) {
        try {
            T result = operation.get();
            return Result.success(successMessage, result);
        } catch (Exception e) {
            log.error("{}: {}", errorMessage, e.getMessage(), e);
            return Result.error(errorMessage + ": " + e.getMessage());
        }
    }
    
    /**
     * 执行无返回值的业务操作并处理异常
     * 
     * @param operation 业务操作
     * @param successMessage 成功消息
     * @param errorMessage 错误消息前缀
     * @return 统一响应结果
     */
    protected Result<Void> executeVoidOperation(Runnable operation, String successMessage, String errorMessage) {
        try {
            operation.run();
            return Result.success(successMessage, null);
        } catch (Exception e) {
            log.error("{}: {}", errorMessage, e.getMessage(), e);
            return Result.error(errorMessage + ": " + e.getMessage());
        }
    }
    
    /**
     * 执行布尔结果的业务操作并处理异常
     * 
     * @param operation 业务操作
     * @param successMessage 成功消息
     * @param failureMessage 失败消息
     * @param errorMessage 错误消息前缀
     * @return 统一响应结果
     */
    protected Result<Void> executeBooleanOperation(Supplier<Boolean> operation, 
                                                  String successMessage, 
                                                  String failureMessage, 
                                                  String errorMessage) {
        try {
            boolean success = operation.get();
            if (success) {
                return Result.success(successMessage, null);
            } else {
                return Result.error(failureMessage);
            }
        } catch (Exception e) {
            log.error("{}: {}", errorMessage, e.getMessage(), e);
            return Result.error(errorMessage + ": " + e.getMessage());
        }
    }
    
    /**
     * 通用的存在性检查方法
     * 
     * @param checkOperation 检查操作
     * @param itemName 检查项名称（如"租户编码"、"角色编码"等）
     * @param value 检查的值
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkExists(Supplier<Boolean> checkOperation, 
                                         String itemName, 
                                         String value, 
                                         Long excludeId) {
        // 参数验证
        if (!StringUtils.hasText(value)) {
            return Result.error(itemName + "不能为空");
        }
        
        try {
            log.info("检查{}: {}, 排除ID: {}", itemName, value, excludeId);
            boolean exists = checkOperation.get();
            return Result.success("检查" + itemName + "完成", exists);
        } catch (Exception e) {
            log.error("检查{}失败: {}", itemName, e.getMessage(), e);
            return Result.error("检查" + itemName + "失败: " + e.getMessage());
        }
    }
    
    /**
     * 通用的编码存在性检查
     * 
     * @param checkOperation 检查操作
     * @param code 编码值
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkCodeExists(Supplier<Boolean> checkOperation, String code, Long excludeId) {
        return checkExists(checkOperation, "编码", code, excludeId);
    }
    
    /**
     * 通用的名称存在性检查
     * 
     * @param checkOperation 检查操作
     * @param name 名称值
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkNameExists(Supplier<Boolean> checkOperation, String name, Long excludeId) {
        return checkExists(checkOperation, "名称", name, excludeId);
    }
    
    /**
     * 通用的路径存在性检查
     * 
     * @param checkOperation 检查操作
     * @param path 路径值
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkPathExists(Supplier<Boolean> checkOperation, String path, Long excludeId) {
        return checkExists(checkOperation, "路径", path, excludeId);
    }
    
    /**
     * 通用的手机号存在性检查
     * 
     * @param checkOperation 检查操作
     * @param phone 手机号
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkPhoneExists(Supplier<Boolean> checkOperation, String phone, Long excludeId) {
        return checkExists(checkOperation, "手机号", phone, excludeId);
    }
    
    /**
     * 通用的邮箱存在性检查
     * 
     * @param checkOperation 检查操作
     * @param email 邮箱
     * @param excludeId 排除的ID
     * @return 统一响应结果
     */
    protected Result<Boolean> checkEmailExists(Supplier<Boolean> checkOperation, String email, Long excludeId) {
        return checkExists(checkOperation, "邮箱", email, excludeId);
    }
    
    /**
     * 通用的获取详情方法
     * 
     * @param getOperation 获取操作
     * @param id 实体ID
     * @param entityName 实体名称
     * @param <T> 实体类型
     * @return 统一响应结果
     */
    protected <T> Result<T> getEntityById(Supplier<T> getOperation, Long id, String entityName) {
        if (id == null) {
            return Result.error(entityName + "ID不能为空");
        }
        
        try {
            T entity = getOperation.get();
            if (entity == null) {
                return Result.error(entityName + "不存在");
            }
            return Result.success("获取" + entityName + "详情成功", entity);
        } catch (Exception e) {
            log.error("获取{}详情失败: {}", entityName, e.getMessage(), e);
            return Result.error("获取" + entityName + "详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 通用的创建方法
     * 
     * @param createOperation 创建操作
     * @param entityName 实体名称
     * @return 统一响应结果
     */
    protected Result<Void> createEntity(Supplier<Boolean> createOperation, String entityName) {
        return executeBooleanOperation(
            createOperation,
            "创建" + entityName + "成功",
            "创建" + entityName + "失败",
            "创建" + entityName + "失败"
        );
    }
    
    /**
     * 通用的更新方法
     * 
     * @param updateOperation 更新操作
     * @param entityName 实体名称
     * @return 统一响应结果
     */
    protected Result<Void> updateEntity(Supplier<Boolean> updateOperation, String entityName) {
        return executeBooleanOperation(
            updateOperation,
            "更新" + entityName + "成功",
            "更新" + entityName + "失败",
            "更新" + entityName + "失败"
        );
    }
    
    /**
     * 通用的删除方法
     * 
     * @param deleteOperation 删除操作
     * @param entityName 实体名称
     * @return 统一响应结果
     */
    protected Result<Void> deleteEntity(Supplier<Boolean> deleteOperation, String entityName) {
        return executeBooleanOperation(
            deleteOperation,
            "删除" + entityName + "成功",
            "删除" + entityName + "失败",
            "删除" + entityName + "失败"
        );
    }
    
    /**
     * 通用的批量删除方法
     * 
     * @param batchDeleteOperation 批量删除操作
     * @param entityName 实体名称
     * @return 统一响应结果
     */
    protected Result<Void> batchDeleteEntity(Supplier<Boolean> batchDeleteOperation, String entityName) {
        return executeBooleanOperation(
            batchDeleteOperation,
            "批量删除" + entityName + "成功",
            "批量删除" + entityName + "失败",
            "批量删除" + entityName + "失败"
        );
    }
    
    /**
     * 通用的初始化方法
     * 
     * @param initOperation 初始化操作
     * @param entityName 实体名称
     * @return 统一响应结果
     */
    protected Result<Void> initializeEntity(Supplier<Boolean> initOperation, String entityName) {
        return executeBooleanOperation(
            initOperation,
            "初始化" + entityName + "成功",
            "初始化" + entityName + "失败",
            "初始化" + entityName + "失败"
        );
    }
    
    /**
     * 验证ID参数
     * 
     * @param id ID值
     * @param entityName 实体名称
     * @return 是否有效
     */
    protected boolean validateId(Long id, String entityName) {
        if (id == null || id <= 0) {
            log.warn("无效的{}ID: {}", entityName, id);
            return false;
        }
        return true;
    }
    
    /**
     * 验证字符串参数
     * 
     * @param value 字符串值
     * @param fieldName 字段名称
     * @return 是否有效
     */
    protected boolean validateString(String value, String fieldName) {
        if (!StringUtils.hasText(value)) {
            log.warn("{}不能为空", fieldName);
            return false;
        }
        return true;
    }
    
    /**
     * 构建成功响应
     * 
     * @param message 消息
     * @param data 数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    protected <T> Result<T> success(String message, T data) {
        return Result.success(message, data);
    }
    
    /**
     * 构建成功响应（无数据）
     * 
     * @param message 消息
     * @return 响应结果
     */
    protected Result<Void> success(String message) {
        return Result.success(message, null);
    }
    
    /**
     * 构建错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    protected <T> Result<T> error(String message) {
        return Result.error(message);
    }
    
    /**
     * 构建错误响应（带状态码）
     * 
     * @param code 状态码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    protected <T> Result<T> error(Integer code, String message) {
        return Result.error(code, message);
    }
    
    /**
     * 构建错误响应（使用ResultCode）
     * 
     * @param resultCode 结果码
     * @param <T> 数据类型
     * @return 响应结果
     */
    protected <T> Result<T> error(ResultCode resultCode) {
        return Result.error(resultCode);
    }
}
