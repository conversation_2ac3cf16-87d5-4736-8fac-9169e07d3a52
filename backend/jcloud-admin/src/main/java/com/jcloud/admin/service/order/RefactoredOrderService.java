package com.jcloud.admin.service.order;

import com.jcloud.admin.dto.response.SubordinateProfit;
import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.admin.service.OrderService;
import com.jcloud.common.dto.ActualSettlementItem;
import com.jcloud.common.dto.BatchSettleRequest;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.vo.OrderVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 重构后的订单服务实现类
 * 将原来1520行的大文件拆分为多个职责单一的服务类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("refactoredOrderService")
@RequiredArgsConstructor
public class RefactoredOrderService implements OrderService {

    private final SysOrderMapper sysOrderMapper;
    private final VimUserMapper vimUserMapper;
    private final OperationsMapper operationsMapper;
    
    // 拆分后的服务类
    private final OrderValidationService validationService;
    private final OrderCalculationService calculationService;
    private final OrderCreationService creationService;
    private final OrderQueryService queryService;
    private final OrderSettlementService settlementService;
    private final OrderExportService exportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SysOrder> createOrders(OrderCreateRequest request) {
        // 获取当前登录用户ID（用于权限验证）
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取目标用户ID（要为哪个用户创建订单）
        Long targetUserId = request.getTargetUserId();
        if (targetUserId == null) {
            throw new BusinessException("目标用户ID不能为空");
        }

        log.info("开始创建佣金结算订单，当前用户ID: {}, 目标用户ID: {}, 请求: {}", 
            currentUserId, targetUserId, request);

        // 1. 参数验证
        validationService.validateCreateRequest(request);
        
        // 2. 查询目标用户信息（使用目标用户ID）
        VimUser targetUser = vimUserMapper.selectByUserId(targetUserId.intValue());
        if (targetUser == null || !targetUser.isActive()) {
            throw new BusinessException("目标用户不存在或已禁用");
        }

        // 3. 检查是否已有重复订单（使用目标用户ID）
        if (validationService.hasExistingOrder(targetUserId.toString(), 
                request.getStartTime(), request.getEndTime())) {
            throw new BusinessException("该时间段已存在结算订单，请勿重复创建");
        }
        
        // 4. 根据目标用户身份创建订单
        List<SysOrder> orders;
        if (targetUser.isAgent()) {
            // 代理用户：创建主订单 + 子订单
            orders = creationService.createAgentOrders(targetUser, request);
        } else if (targetUser.isAnchor()) {
            // 主播用户：创建单个订单
            orders = creationService.createAnchorOrders(targetUser, request);
        } else {
            throw new BusinessException("目标用户不是代理或主播，无法创建结算订单");
        }

        // 5. 批量插入订单
        if (!CollectionUtils.isEmpty(orders)) {
            int insertCount = sysOrderMapper.insertBatch(orders);
            log.info("批量插入订单完成，插入数量: {}", insertCount);
        }

        log.info("佣金结算订单创建完成，共创建 {} 个订单", orders.size());
        return orders;
    }

    @Override
    public PageResult<OrderVO> pageOrders(OrderQueryRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        return queryService.pageOrders(request, currentUserId.toString());
    }

    @Override
    public OrderVO getOrderDetail(String payid) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return queryService.getOrderDetail(payid, currentUserId.toString());
    }

    @Override
    public OrderVO getMainOrderWithSubOrders(String parentsPayid) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return queryService.getMainOrderWithSubOrders(parentsPayid, currentUserId.toString());
    }

    @Override
    public List<OrderVO> getSubOrdersByMainOrderId(String mainOrderId) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return queryService.getSubOrdersByMainOrderId(mainOrderId, currentUserId.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int settleOrders(List<String> payids) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return settlementService.settleOrders(payids, currentUserId.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int settleOrdersWithActualAmount(BatchSettleRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return settlementService.settleOrdersWithActualAmount(request, currentUserId.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String payid) {
        return cancelOrders(Collections.singletonList(payid)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelOrders(List<String> payids) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return settlementService.cancelOrders(payids, currentUserId.toString());
    }

    @Override
    public boolean canCreateOrder(OrderCreateRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            return false;
        }
        
        return settlementService.canCreateOrder(request, currentUserId.toString());
    }

    @Override
    public OrderStatistics getOrderStatistics() {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("获取订单统计信息，用户ID: {}", currentUserId);

        // 构建统计查询条件
        String userIdStr = currentUserId.toString();
        
        // 查询各种状态的订单数量
        long totalCount = sysOrderMapper.countOrdersByUser(userIdStr);
        long pendingCount = sysOrderMapper.countOrdersByUserAndStatus(userIdStr, 0);
        long settledCount = sysOrderMapper.countOrdersByUserAndStatus(userIdStr, 1);
        long cancelledCount = sysOrderMapper.countOrdersByUserAndStatus(userIdStr, 2);
        
        // 查询金额统计
        BigDecimal totalAmount = sysOrderMapper.sumAmountByUser(userIdStr, "total_amount");
        BigDecimal commissionAmount = sysOrderMapper.sumAmountByUser(userIdStr, "commission_amount");
        BigDecimal actualAmount = sysOrderMapper.sumAmountByUser(userIdStr, "actual_amount");
        
        // 构建统计结果
        OrderStatistics statistics = new OrderStatistics();
        statistics.setTotalCount(totalCount);
        statistics.setPendingCount(pendingCount);
        statistics.setSettledCount(settledCount);
        statistics.setCancelledCount(cancelledCount);
        statistics.setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO);
        statistics.setCommissionAmount(commissionAmount != null ? commissionAmount : BigDecimal.ZERO);
        statistics.setActualAmount(actualAmount != null ? actualAmount : BigDecimal.ZERO);

        log.info("订单统计信息获取完成: {}", statistics);
        return statistics;
    }

    @Override
    public Map<String, Object> getOrderFinancialStats(Integer startTime, Integer endTime) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("获取订单财务统计，用户ID: {}, 时间范围: {} - {}", currentUserId, startTime, endTime);

        try {
            // 调用存储过程获取下级利润统计
            SubordinateProfit profit = operationsMapper.getSubordinateProfit(
                currentUserId.intValue(), 
                5, // 最大层级
                startTime.toString(),
                endTime.toString()
            );

            Map<String, Object> stats = new HashMap<>();
            if (profit != null) {
                stats.put("totalUsers", profit.getZongyonghu());
                stats.put("totalRecharge", profit.getZongchongzhi());
                stats.put("totalProfit", profit.getZonglirun());
            } else {
                stats.put("totalUsers", 0);
                stats.put("totalRecharge", BigDecimal.ZERO);
                stats.put("totalProfit", BigDecimal.ZERO);
            }

            // 添加时间范围信息
            stats.put("startTime", startTime);
            stats.put("endTime", endTime);
            stats.put("queryTime", System.currentTimeMillis() / 1000);

            log.info("订单财务统计获取完成: {}", stats);
            return stats;
        } catch (Exception e) {
            log.error("获取订单财务统计失败", e);
            throw new BusinessException("获取财务统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<OrderVO> exportOrders(OrderQueryRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return queryService.exportOrders(request, currentUserId.toString());
    }

    @Override
    public byte[] exportOrdersToExcel(OrderQueryRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        
        return exportService.exportOrdersToExcel(request, currentUserId.toString());
    }
    
    /**
     * 订单统计信息类
     */
    public static class OrderStatistics {
        private long totalCount;
        private long pendingCount;
        private long settledCount;
        private long cancelledCount;
        private BigDecimal totalAmount;
        private BigDecimal commissionAmount;
        private BigDecimal actualAmount;
        
        // Getters and Setters
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
        
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }
        
        public long getSettledCount() { return settledCount; }
        public void setSettledCount(long settledCount) { this.settledCount = settledCount; }
        
        public long getCancelledCount() { return cancelledCount; }
        public void setCancelledCount(long cancelledCount) { this.cancelledCount = cancelledCount; }
        
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        
        public BigDecimal getCommissionAmount() { return commissionAmount; }
        public void setCommissionAmount(BigDecimal commissionAmount) { this.commissionAmount = commissionAmount; }
        
        public BigDecimal getActualAmount() { return actualAmount; }
        public void setActualAmount(BigDecimal actualAmount) { this.actualAmount = actualAmount; }
        
        @Override
        public String toString() {
            return String.format("OrderStatistics{totalCount=%d, pendingCount=%d, settledCount=%d, " +
                "cancelledCount=%d, totalAmount=%s, commissionAmount=%s, actualAmount=%s}",
                totalCount, pendingCount, settledCount, cancelledCount, 
                totalAmount, commissionAmount, actualAmount);
        }
    }
}
