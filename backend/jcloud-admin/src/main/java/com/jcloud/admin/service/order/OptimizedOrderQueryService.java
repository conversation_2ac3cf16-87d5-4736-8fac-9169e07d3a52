package com.jcloud.admin.service.order;

import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.vo.OrderVO;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jcloud.common.entity.table.SysOrderTableDef.SYS_ORDER;
import static com.jcloud.common.entity.table.VimUserTableDef.VIM_USER;

/**
 * 优化后的订单查询服务
 * 使用MyBatis-Flex QueryWrapper替代XML映射，提升性能和可维护性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizedOrderQueryService {
    
    private final SysOrderMapper sysOrderMapper;
    private final VimUserMapper vimUserMapper;
    
    /**
     * 优化的分页查询订单（使用QueryWrapper）
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 分页结果
     */
    public PageResult<OrderVO> pageOrdersOptimized(OrderQueryRequest request, String currentUserId) {
        log.info("优化分页查询订单列表，用户ID: {}, 请求: {}", currentUserId, request);
        
        // 使用MyBatis-Flex QueryWrapper构建复杂查询
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.ALL_COLUMNS)
            .from(SYS_ORDER)
            .where(SYS_ORDER.PARENTS_PAYID.isNull()) // 只查询主订单
            .and(buildUserPermissionCondition(currentUserId))
            .and(buildTimeRangeCondition(request))
            .and(buildStatusCondition(request))
            .and(buildUserFilterCondition(request))
            .orderBy(SYS_ORDER.CREATE_TIME.desc());
        
        // 分页查询
        Page<SysOrder> page = Page.of(request.getPageNum(), request.getPageSize());
        Page<SysOrder> result = sysOrderMapper.paginate(page, queryWrapper);
        
        // 批量转换为VO
        List<OrderVO> orderVOs = batchConvertToOrderVOs(result.getRecords());
        
        return PageResult.of(orderVOs, result.getTotalRow(), request.getPageNum(), request.getPageSize());
    }
    
    /**
     * 多表关联查询订单（使用JOIN优化）
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 订单列表
     */
    public List<OrderVO> queryOrdersWithUserInfo(OrderQueryRequest request, String currentUserId) {
        log.info("多表关联查询订单，用户ID: {}, 请求: {}", currentUserId, request);
        
        // 使用LEFT JOIN一次性查询订单和用户信息，避免N+1查询
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                SYS_ORDER.ALL_COLUMNS,
                VIM_USER.NICKNAME.as("userNickname"),
                VIM_USER.PHONE.as("userPhone")
            )
            .from(SYS_ORDER)
            .leftJoin(VIM_USER).on(SYS_ORDER.USER_ID.eq(VIM_USER.ID.cast(String.class)))
            .where(SYS_ORDER.PARENTS_PAYID.isNull())
            .and(buildUserPermissionCondition(currentUserId))
            .and(buildTimeRangeCondition(request))
            .and(buildStatusCondition(request))
            .orderBy(SYS_ORDER.CREATE_TIME.desc());
        
        // 执行查询并转换结果
        List<SysOrder> orders = sysOrderMapper.selectListByQuery(queryWrapper);
        return convertToOrderVOsWithUserInfo(orders);
    }
    
    /**
     * 统计查询（使用聚合函数）
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 统计结果
     */
    public Map<String, Object> getOrderStatistics(OrderQueryRequest request, String currentUserId) {
        log.info("统计查询订单，用户ID: {}, 请求: {}", currentUserId, request);
        
        // 使用聚合函数进行统计查询
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(
                SYS_ORDER.ORDER_STATUS,
                SYS_ORDER.ORDER_STATUS.count().as("orderCount"),
                SYS_ORDER.TOTAL_AMOUNT.sum().as("totalAmount"),
                SYS_ORDER.COMMISSION_AMOUNT.sum().as("totalCommission"),
                SYS_ORDER.ACTUAL_AMOUNT.sum().as("totalActualAmount")
            )
            .from(SYS_ORDER)
            .where(SYS_ORDER.PARENTS_PAYID.isNull())
            .and(buildUserPermissionCondition(currentUserId))
            .and(buildTimeRangeCondition(request))
            .groupBy(SYS_ORDER.ORDER_STATUS);
        
        // 执行统计查询
        List<Map<String, Object>> results = sysOrderMapper.selectListByQueryAs(queryWrapper, Map.class);
        
        // 转换统计结果
        return convertStatisticsResults(results);
    }
    
    /**
     * 子查询示例：查询有子订单的主订单
     * 
     * @param currentUserId 当前用户ID
     * @return 订单列表
     */
    public List<OrderVO> queryMainOrdersWithSubOrders(String currentUserId) {
        log.info("查询有子订单的主订单，用户ID: {}", currentUserId);
        
        // 使用EXISTS子查询
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_ORDER.ALL_COLUMNS)
            .from(SYS_ORDER.as("main"))
            .where(SYS_ORDER.PARENTS_PAYID.isNull())
            .and(buildUserPermissionCondition(currentUserId))
            .and(QueryWrapper.exists(
                QueryWrapper.create()
                    .select()
                    .from(SYS_ORDER.as("sub"))
                    .where(SYS_ORDER.PARENTS_PAYID.eq(SYS_ORDER.PAYID))
            ))
            .orderBy(SYS_ORDER.CREATE_TIME.desc());
        
        List<SysOrder> orders = sysOrderMapper.selectListByQuery(queryWrapper);
        return batchConvertToOrderVOs(orders);
    }
    
    /**
     * 构建用户权限条件
     */
    private QueryWrapper buildUserPermissionCondition(String currentUserId) {
        QueryWrapper condition = QueryWrapper.create();
        
        // 管理员可以查看所有订单，普通用户只能查看自己相关的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            condition.and(wrapper -> wrapper
                .eq(SYS_ORDER.USER_ID, currentUserId)
                .or()
                .eq(SYS_ORDER.AGENT, currentUserId)
            );
        }
        
        return condition;
    }
    
    /**
     * 构建时间范围条件
     */
    private QueryWrapper buildTimeRangeCondition(OrderQueryRequest request) {
        QueryWrapper condition = QueryWrapper.create();
        
        if (request.getStartTime() != null) {
            condition.ge(SYS_ORDER.START_TIME, request.getStartTime());
        }
        if (request.getEndTime() != null) {
            condition.le(SYS_ORDER.END_TIME, request.getEndTime());
        }
        
        return condition;
    }
    
    /**
     * 构建状态条件
     */
    private QueryWrapper buildStatusCondition(OrderQueryRequest request) {
        QueryWrapper condition = QueryWrapper.create();
        
        if (request.getOrderStatus() != null) {
            condition.eq(SYS_ORDER.ORDER_STATUS, request.getOrderStatus());
        }
        if (request.getSettlementStatus() != null) {
            condition.eq(SYS_ORDER.SETTLEMENT_STATUS, request.getSettlementStatus());
        }
        
        return condition;
    }
    
    /**
     * 构建用户过滤条件
     */
    private QueryWrapper buildUserFilterCondition(OrderQueryRequest request) {
        QueryWrapper condition = QueryWrapper.create();
        
        if (StringUtils.hasText(request.getUserId())) {
            condition.eq(SYS_ORDER.USER_ID, request.getUserId());
        }
        if (StringUtils.hasText(request.getAgent())) {
            condition.eq(SYS_ORDER.AGENT, request.getAgent());
        }
        if (StringUtils.hasText(request.getAnchor())) {
            condition.eq(SYS_ORDER.ANCHOR, request.getAnchor());
        }
        if (StringUtils.hasText(request.getPayid())) {
            condition.like(SYS_ORDER.PAYID, request.getPayid());
        }
        
        return condition;
    }
    
    /**
     * 批量转换为OrderVO
     */
    private List<OrderVO> batchConvertToOrderVOs(List<SysOrder> orders) {
        if (orders.isEmpty()) {
            return List.of();
        }
        
        // 批量查询用户信息，避免N+1查询
        List<String> userIds = orders.stream()
            .flatMap(order -> List.of(order.getUserId(), order.getAgent(), order.getAnchor()).stream())
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());
        
        Map<String, VimUser> userMap = batchQueryUsers(userIds);
        
        // 转换为VO
        return orders.stream()
            .map(order -> convertToOrderVO(order, userMap))
            .collect(Collectors.toList());
    }
    
    /**
     * 批量查询用户信息
     */
    private Map<String, VimUser> batchQueryUsers(List<String> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }
        
        // 使用IN查询批量获取用户信息
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(VIM_USER.ALL_COLUMNS)
            .from(VIM_USER)
            .where(VIM_USER.ID.in(userIds.stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList())));
        
        List<VimUser> users = vimUserMapper.selectListByQuery(queryWrapper);
        return users.stream()
            .collect(Collectors.toMap(
                user -> user.getId().toString(),
                user -> user,
                (existing, replacement) -> existing
            ));
    }
    
    /**
     * 转换为OrderVO
     */
    private OrderVO convertToOrderVO(SysOrder order, Map<String, VimUser> userMap) {
        OrderVO vo = new OrderVO();
        
        // 复制基本属性
        vo.setPayid(order.getPayid());
        vo.setParentsPayid(order.getParentsPayid());
        vo.setUserId(order.getUserId());
        vo.setAgent(order.getAgent());
        vo.setAnchor(order.getAnchor());
        vo.setStartTime(order.getStartTime());
        vo.setEndTime(order.getEndTime());
        vo.setCreateTime(order.getCreateTime());
        vo.setSettlementTime(order.getSettlementTime());
        vo.setTotalAmount(order.getTotalAmount());
        vo.setCommissionAmount(order.getCommissionAmount());
        vo.setActualAmount(order.getActualAmount());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setSettlementStatus(order.getSettlementStatus());
        
        // 设置用户昵称
        if (StringUtils.hasText(order.getUserId())) {
            VimUser user = userMap.get(order.getUserId());
            if (user != null) {
                vo.setUserNickname(user.getNickname());
            }
        }
        
        if (StringUtils.hasText(order.getAgent())) {
            VimUser agent = userMap.get(order.getAgent());
            if (agent != null) {
                vo.setAgentNickname(agent.getNickname());
            }
        }
        
        if (StringUtils.hasText(order.getAnchor())) {
            VimUser anchor = userMap.get(order.getAnchor());
            if (anchor != null) {
                vo.setAnchorNickname(anchor.getNickname());
            }
        }
        
        return vo;
    }
    
    /**
     * 转换带用户信息的订单VO
     */
    private List<OrderVO> convertToOrderVOsWithUserInfo(List<SysOrder> orders) {
        return orders.stream()
            .map(this::convertToOrderVOWithUserInfo)
            .collect(Collectors.toList());
    }
    
    /**
     * 转换单个带用户信息的订单VO
     */
    private OrderVO convertToOrderVOWithUserInfo(SysOrder order) {
        OrderVO vo = new OrderVO();
        
        // 复制基本属性
        vo.setPayid(order.getPayid());
        vo.setParentsPayid(order.getParentsPayid());
        vo.setUserId(order.getUserId());
        vo.setAgent(order.getAgent());
        vo.setAnchor(order.getAnchor());
        vo.setStartTime(order.getStartTime());
        vo.setEndTime(order.getEndTime());
        vo.setCreateTime(order.getCreateTime());
        vo.setSettlementTime(order.getSettlementTime());
        vo.setTotalAmount(order.getTotalAmount());
        vo.setCommissionAmount(order.getCommissionAmount());
        vo.setActualAmount(order.getActualAmount());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setSettlementStatus(order.getSettlementStatus());
        
        // 用户信息已经通过JOIN查询获取，直接设置
        // 注意：这里需要根据实际的查询结果映射来设置
        
        return vo;
    }
    
    /**
     * 转换统计结果
     */
    private Map<String, Object> convertStatisticsResults(List<Map<String, Object>> results) {
        Map<String, Object> statistics = Map.of(
            "totalOrders", results.stream().mapToLong(r -> (Long) r.get("orderCount")).sum(),
            "totalAmount", results.stream().mapToDouble(r -> ((Number) r.get("totalAmount")).doubleValue()).sum(),
            "totalCommission", results.stream().mapToDouble(r -> ((Number) r.get("totalCommission")).doubleValue()).sum(),
            "totalActualAmount", results.stream().mapToDouble(r -> ((Number) r.get("totalActualAmount")).doubleValue()).sum(),
            "statusBreakdown", results
        );
        
        return statistics;
    }
}
