package com.jcloud.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 充值数据DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "充值数据")
public class RechargeData {
    
    /**
     * 日期
     */
    @Schema(description = "日期", example = "2025-01-20")
    private String date;
    
    /**
     * 充值次数
     */
    @Schema(description = "充值次数", example = "50")
    private Integer rechargeCount;
    
    /**
     * 总充值金额
     */
    @Schema(description = "总充值金额", example = "10000.00")
    private BigDecimal totalAmount;
    
    /**
     * 平均充值金额
     */
    @Schema(description = "平均充值金额", example = "200.00")
    private BigDecimal avgAmount;
}
