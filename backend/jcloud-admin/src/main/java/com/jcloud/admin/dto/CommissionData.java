package com.jcloud.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 佣金数据DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "佣金数据")
public class CommissionData {
    
    /**
     * 日期
     */
    @Schema(description = "日期", example = "2025-01-20")
    private String date;
    
    /**
     * 佣金订单数
     */
    @Schema(description = "佣金订单数", example = "10")
    private Integer commissionOrders;
    
    /**
     * 总充值金额（佣金计算基础）
     */
    @Schema(description = "总充值金额", example = "30000.00")
    private BigDecimal totalRechargeAmount;
    
    /**
     * 总佣金金额
     */
    @Schema(description = "总佣金金额", example = "3000.00")
    private BigDecimal totalCommission;
    
    /**
     * 平均佣金比例
     */
    @Schema(description = "平均佣金比例", example = "10.0")
    private BigDecimal avgCommissionRate;
}
