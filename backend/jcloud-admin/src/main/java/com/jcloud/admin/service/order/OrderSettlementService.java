package com.jcloud.admin.service.order;

import com.jcloud.common.dto.ActualSettlementItem;
import com.jcloud.common.dto.BatchSettleRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.enums.OrderStatus;
import com.jcloud.common.enums.SettlementStatus;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单结算服务
 * 负责订单结算相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderSettlementService {
    
    private final SysOrderMapper sysOrderMapper;
    private final OrderValidationService validationService;
    private final OrderCalculationService calculationService;
    
    /**
     * 批量结算订单（使用原始金额）
     * 
     * @param payids 订单ID列表
     * @param currentUserId 当前用户ID
     * @return 结算成功的订单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int settleOrders(List<String> payids, String currentUserId) {
        log.info("开始批量结算订单，用户ID: {}, 订单数量: {}", currentUserId, payids.size());
        
        // 验证订单所有权
        validationService.validateOrdersOwnership(payids, currentUserId);
        
        int settledCount = 0;
        for (String payid : payids) {
            try {
                if (settleOrder(payid)) {
                    settledCount++;
                }
            } catch (Exception e) {
                log.error("结算订单失败: {}", payid, e);
                // 继续处理其他订单，不中断整个流程
            }
        }
        
        log.info("批量结算完成，成功结算 {} 个订单", settledCount);
        return settledCount;
    }
    
    /**
     * 批量结算订单（使用实际金额）
     * 
     * @param request 批量结算请求
     * @param currentUserId 当前用户ID
     * @return 结算成功的订单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int settleOrdersWithActualAmount(BatchSettleRequest request, String currentUserId) {
        log.info("开始批量结算订单（实际金额），用户ID: {}, 订单数量: {}", 
            currentUserId, request.getItems().size());
        
        // 提取订单ID列表进行权限验证
        List<String> payids = request.getItems().stream()
            .map(ActualSettlementItem::getPayid)
            .collect(Collectors.toList());
        
        // 验证订单所有权
        validationService.validateOrdersOwnership(payids, currentUserId);
        
        // 创建实际金额映射
        Map<String, BigDecimal> actualAmountMap = request.getItems().stream()
            .collect(Collectors.toMap(
                ActualSettlementItem::getPayid,
                ActualSettlementItem::getActualAmount,
                (existing, replacement) -> replacement
            ));
        
        int settledCount = 0;
        for (String payid : payids) {
            try {
                BigDecimal actualAmount = actualAmountMap.get(payid);
                if (settleOrderWithActualAmount(payid, actualAmount)) {
                    settledCount++;
                }
            } catch (Exception e) {
                log.error("结算订单失败: {}", payid, e);
                // 继续处理其他订单，不中断整个流程
            }
        }
        
        log.info("批量结算完成，成功结算 {} 个订单", settledCount);
        return settledCount;
    }
    
    /**
     * 结算单个订单（使用原始金额）
     * 
     * @param payid 订单ID
     * @return 是否结算成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean settleOrder(String payid) {
        log.debug("开始结算订单: {}", payid);
        
        // 查询订单
        SysOrder order = sysOrderMapper.selectOneById(payid);
        if (order == null) {
            throw new BusinessException("订单不存在: " + payid);
        }
        
        // 验证订单状态
        validationService.validateOrderCanSettle(order);
        
        // 更新订单状态
        order.setOrderStatus(OrderStatus.SETTLED.getCode());
        order.setSettlementStatus(SettlementStatus.SETTLED.getCode());
        order.setSettlementTime(TimeUtil.getCurrentTimestamp());
        order.setUpdateBy(SecurityUtils.getUserId());
        
        // 保存更新
        int updateCount = sysOrderMapper.update(order);
        boolean success = updateCount > 0;
        
        if (success) {
            log.info("订单结算成功: {}", payid);
        } else {
            log.error("订单结算失败: {}", payid);
        }
        
        return success;
    }
    
    /**
     * 结算单个订单（使用实际金额）
     * 
     * @param payid 订单ID
     * @param actualAmount 实际结算金额
     * @return 是否结算成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean settleOrderWithActualAmount(String payid, BigDecimal actualAmount) {
        log.debug("开始结算订单（实际金额）: {}, 实际金额: {}", payid, actualAmount);
        
        // 查询订单
        SysOrder order = sysOrderMapper.selectOneById(payid);
        if (order == null) {
            throw new BusinessException("订单不存在: " + payid);
        }
        
        // 验证订单状态
        validationService.validateOrderCanSettle(order);
        
        // 验证实际金额
        if (!calculationService.isValidAmount(actualAmount)) {
            throw new BusinessException("实际结算金额无效: " + actualAmount);
        }
        
        // 更新订单状态和实际金额
        order.setActualAmount(actualAmount);
        order.setOrderStatus(OrderStatus.SETTLED.getCode());
        order.setSettlementStatus(SettlementStatus.SETTLED.getCode());
        order.setSettlementTime(TimeUtil.getCurrentTimestamp());
        order.setUpdateBy(SecurityUtils.getUserId());
        
        // 保存更新
        int updateCount = sysOrderMapper.update(order);
        boolean success = updateCount > 0;
        
        if (success) {
            log.info("订单结算成功（实际金额）: {}, 实际金额: {}", payid, actualAmount);
        } else {
            log.error("订单结算失败（实际金额）: {}", payid);
        }
        
        return success;
    }
    
    /**
     * 取消单个订单
     * 
     * @param payid 订单ID
     * @param currentUserId 当前用户ID
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String payid, String currentUserId) {
        log.debug("开始取消订单: {}", payid);
        
        // 查询订单
        SysOrder order = sysOrderMapper.selectOneById(payid);
        if (order == null) {
            throw new BusinessException("订单不存在: " + payid);
        }
        
        // 验证订单所有权
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            if (validationService.isUserOrder(order, currentUserId)) {
                throw new BusinessException("无权取消此订单");
            }
        }
        
        // 验证订单状态
        validationService.validateOrderCanCancel(order);
        
        // 更新订单状态
        order.setOrderStatus(OrderStatus.CANCELLED.getCode());
        order.setSettlementStatus(SettlementStatus.CANCELLED.getCode());
        order.setUpdateBy(SecurityUtils.getUserId());
        
        // 保存更新
        int updateCount = sysOrderMapper.update(order);
        boolean success = updateCount > 0;
        
        if (success) {
            log.info("订单取消成功: {}", payid);
        } else {
            log.error("订单取消失败: {}", payid);
        }
        
        return success;
    }
    
    /**
     * 批量取消订单
     * 
     * @param payids 订单ID列表
     * @param currentUserId 当前用户ID
     * @return 取消成功的订单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int cancelOrders(List<String> payids, String currentUserId) {
        log.info("开始批量取消订单，用户ID: {}, 订单数量: {}", currentUserId, payids.size());
        
        // 验证订单所有权
        validationService.validateOrdersOwnership(payids, currentUserId);
        
        int cancelledCount = 0;
        for (String payid : payids) {
            try {
                if (cancelOrder(payid, currentUserId)) {
                    cancelledCount++;
                }
            } catch (Exception e) {
                log.error("取消订单失败: {}", payid, e);
                // 继续处理其他订单，不中断整个流程
            }
        }
        
        log.info("批量取消完成，成功取消 {} 个订单", cancelledCount);
        return cancelledCount;
    }
    
    /**
     * 检查是否可以创建订单
     * 
     * @param request 创建请求
     * @param currentUserId 当前用户ID
     * @return 是否可以创建
     */
    public boolean canCreateOrder(Object request, String currentUserId) {
        try {
            // 这里可以添加更多的业务规则检查
            // 例如：检查用户权限、检查时间范围、检查重复订单等
            
            // 基本权限检查
            if (!SecurityUtils.hasPermission("order:create")) {
                return false;
            }
            
            // 其他业务规则检查...
            
            return true;
        } catch (Exception e) {
            log.error("检查订单创建权限失败", e);
            return false;
        }
    }
}
