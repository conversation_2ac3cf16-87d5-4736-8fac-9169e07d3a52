package com.jcloud.admin.controller.base;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 检查Controller基类
 * 提供通用的检查接口模板
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class CheckController extends BaseController {
    
    /**
     * 通用检查接口（使用参数对象）
     *
     * @param request 检查请求参数
     * @param checkFunction 检查函数
     * @return 检查结果
     */
    protected Result<Boolean> performCheck(CheckRequest request,
                                          BiFunction<String, Long, Boolean> checkFunction) {
        if (!request.isValid()) {
            return Result.error(request.getDisplayName() + "参数无效");
        }

        return checkExists(
            () -> checkFunction.apply(request.getValue(), request.getExcludeId()),
            request.getDisplayName(),
            request.getValue(),
            request.getExcludeId()
        );
    }

    /**
     * 带父级ID的检查接口（使用参数对象）
     *
     * @param request 检查请求参数
     * @param checkFunction 检查函数（参数：值、父级ID、排除ID）
     * @return 检查结果
     */
    protected Result<Boolean> performCheckWithParent(CheckRequest request,
                                                    TriFunction<String, Long, Long, Boolean> checkFunction) {
        if (!request.isValid()) {
            return Result.error(request.getDisplayName() + "参数无效");
        }

        return checkExists(
            () -> checkFunction.apply(request.getValue(), request.getParentId(), request.getExcludeId()),
            request.getDisplayName(),
            request.getValue(),
            request.getExcludeId()
        );
    }
    
    /**
     * 编码检查的便捷方法
     */
    protected Result<Boolean> checkCode(String code, Long excludeId,
                                       BiFunction<String, Long, Boolean> checkFunction) {
        return performCheck(CheckRequest.forCode(code, excludeId), checkFunction);
    }

    /**
     * 名称检查的便捷方法
     */
    protected Result<Boolean> checkName(String name, Long excludeId,
                                       BiFunction<String, Long, Boolean> checkFunction) {
        return performCheck(CheckRequest.forName(name, excludeId), checkFunction);
    }

    /**
     * 路径检查的便捷方法
     */
    protected Result<Boolean> checkPath(String path, Long excludeId,
                                       BiFunction<String, Long, Boolean> checkFunction) {
        return performCheck(CheckRequest.forPath(path, excludeId), checkFunction);
    }

    /**
     * 手机号检查的便捷方法
     */
    protected Result<Boolean> checkPhone(String phone, Long excludeId,
                                        BiFunction<String, Long, Boolean> checkFunction) {
        return performCheck(CheckRequest.forPhone(phone, excludeId), checkFunction);
    }

    /**
     * 邮箱检查的便捷方法
     */
    protected Result<Boolean> checkEmail(String email, Long excludeId,
                                        BiFunction<String, Long, Boolean> checkFunction) {
        return performCheck(CheckRequest.forEmail(email, excludeId), checkFunction);
    }

    /**
     * 带父级ID的名称检查便捷方法
     */
    protected Result<Boolean> checkNameWithParent(String name, Long parentId, Long excludeId,
                                                 TriFunction<String, Long, Long, Boolean> checkFunction) {
        return performCheckWithParent(CheckRequest.forNameWithParent(name, parentId, excludeId), checkFunction);
    }
    
    /**
     * 三参数函数接口
     */
    @FunctionalInterface
    public interface TriFunction<T, U, V, R> {
        R apply(T t, U u, V v);
    }
    
    /**
     * 创建标准的编码检查接口
     * 使用此方法可以快速创建符合规范的编码检查接口
     * 
     * 使用示例：
     * <pre>
     * &#64;GetMapping("/check-code")
     * &#64;Operation(summary = "检查租户编码", description = "检查租户编码是否已存在")
     * &#64;SaCheckPermission("system:tenant:view")
     * public Result&lt;Boolean&gt; checkTenantCodeExists(
     *         &#64;Parameter(description = "租户编码", required = true) &#64;RequestParam String tenantCode,
     *         &#64;Parameter(description = "排除的租户ID") &#64;RequestParam(required = false) Long excludeId) {
     *     return createCodeCheckEndpoint(tenantCode, excludeId, tenantService::checkTenantCodeExists);
     * }
     * </pre>
     */
    protected Result<Boolean> createCodeCheckEndpoint(String code, 
                                                     Long excludeId, 
                                                     BiFunction<String, Long, Boolean> checkFunction) {
        return checkCodeExists(() -> checkFunction.apply(code, excludeId), code, excludeId);
    }
    
    /**
     * 创建标准的名称检查接口
     * 使用此方法可以快速创建符合规范的名称检查接口
     */
    protected Result<Boolean> createNameCheckEndpoint(String name, 
                                                     Long excludeId, 
                                                     BiFunction<String, Long, Boolean> checkFunction) {
        return checkNameExists(() -> checkFunction.apply(name, excludeId), name, excludeId);
    }
    
    /**
     * 创建标准的路径检查接口
     * 使用此方法可以快速创建符合规范的路径检查接口
     */
    protected Result<Boolean> createPathCheckEndpoint(String path, 
                                                     Long excludeId, 
                                                     BiFunction<String, Long, Boolean> checkFunction) {
        return checkPathExists(() -> checkFunction.apply(path, excludeId), path, excludeId);
    }
    
    /**
     * 创建标准的手机号检查接口
     * 使用此方法可以快速创建符合规范的手机号检查接口
     */
    protected Result<Boolean> createPhoneCheckEndpoint(String phone, 
                                                      Long excludeId, 
                                                      BiFunction<String, Long, Boolean> checkFunction) {
        return checkPhoneExists(() -> checkFunction.apply(phone, excludeId), phone, excludeId);
    }
    
    /**
     * 创建带父级ID的名称检查接口
     * 用于层级结构的名称唯一性检查
     */
    protected Result<Boolean> createNameWithParentCheckEndpoint(String name, 
                                                               Long parentId, 
                                                               Long excludeId, 
                                                               TriFunction<String, Long, Long, Boolean> checkFunction) {
        return checkExists(() -> checkFunction.apply(name, parentId, excludeId), "名称", name, excludeId);
    }
    
    /**
     * 创建权限编码唯一性检查接口
     * 专门用于权限编码的唯一性验证
     */
    protected Result<Boolean> createPermissionCodeCheckEndpoint(String permissionCode, 
                                                               Long excludeId, 
                                                               Function<String, Object> getByCodeFunction) {
        try {
            Object existingEntity = getByCodeFunction.apply(permissionCode);
            boolean isUnique = existingEntity == null || 
                             (excludeId != null && isEntityIdEquals(existingEntity, excludeId));
            return Result.success(isUnique);
        } catch (Exception e) {
            log.error("验证权限编码唯一性失败: {}", e.getMessage(), e);
            return Result.error("验证权限编码失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查实体ID是否相等
     * 这是一个辅助方法，用于比较实体的ID
     */
    private boolean isEntityIdEquals(Object entity, Long excludeId) {
        try {
            // 使用反射获取实体的ID字段
            java.lang.reflect.Method getIdMethod = entity.getClass().getMethod("getId");
            Object entityId = getIdMethod.invoke(entity);
            return excludeId.equals(entityId);
        } catch (Exception e) {
            log.warn("无法获取实体ID进行比较: {}", e.getMessage());
            return false;
        }
    }
}
