package com.jcloud.admin.service.impl;

import com.jcloud.admin.dto.BusinessRevenueData;
import com.jcloud.admin.dto.CommissionData;
import com.jcloud.admin.dto.RechargeData;
import com.jcloud.admin.service.BusinessDataService;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.mapper.VimBattleMapper;
import com.jcloud.common.mapper.VimOrderBoxMapper;
import com.jcloud.common.mapper.VimOrderRechargeMapper;
import com.jcloud.common.mapper.VimOrderRechargeMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务数据服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("businessDataServiceImpl")  // 明确指定Bean名称，避免冲突
@RequiredArgsConstructor
public class BusinessDataServiceImpl implements BusinessDataService {

    private final VimOrderBoxMapper vimOrderBoxMapper;
    private final VimOrderRechargeMapper vimOrderRechargeMapper;
    private final SysOrderMapper sysOrderMapper;
    private final VimBattleMapper vimBattleMapper;

    @Override
    public List<BusinessRevenueData> getBusinessRevenueTrend(FinancialStatsRequest request) {
        log.info("获取业务收入趋势数据，请求参数：{}", request);

        try {
            // 转换时间格式为时间戳（毫秒）
            String startTimestamp = convertToTimestamp(request.getStartTime());
            String endTimestamp = convertToTimestamp(request.getEndTime());

            log.debug("时间戳转换 - 开始：{}，结束：{}", startTimestamp, endTimestamp);

            // 查询vim_order_box表数据
            List<VimOrderBoxMapper.BusinessRevenueStats> boxData =
                    vimOrderBoxMapper.getBusinessRevenueByDateAndType(startTimestamp, endTimestamp);

            // 查询对战数据（时间戳转换为秒）
            Integer startSecond = convertToSecondTimestamp(request.getStartTime());
            Integer endSecond = convertToSecondTimestamp(request.getEndTime());
            List<VimBattleMapper.BattleRevenueStats> battleData =
                    vimBattleMapper.getBattleRevenueByDate(startSecond, endSecond);

            // 整合数据
            List<BusinessRevenueData> result = getBusinessRevenueData(boxData, battleData);

            log.info("业务收入趋势数据查询成功，返回 {} 条记录（包含对战数据）", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取业务收入趋势数据失败", e);
            throw new RuntimeException("获取业务收入趋势数据失败：" + e.getMessage(), e);
        }
    }

    private static List<BusinessRevenueData> getBusinessRevenueData(List<VimOrderBoxMapper.BusinessRevenueStats> boxData, List<VimBattleMapper.BattleRevenueStats> battleData) {
        List<BusinessRevenueData> result = new ArrayList<>();

        // 处理vim_order_box数据
        for (VimOrderBoxMapper.BusinessRevenueStats stats : boxData) {
            BusinessRevenueData data = new BusinessRevenueData();
            data.setDate(stats.getDate());
            data.setBusinessType(stats.getBusinessType());
            data.setOrderCount(stats.getOrderCount());
            data.setTotalAmount(stats.getTotalAmount());
            data.setAvgAmount(stats.getAvgAmount());
            result.add(data);
        }

        // 处理对战数据（业务类型设为3）
        for (VimBattleMapper.BattleRevenueStats stats : battleData) {
            BusinessRevenueData data = new BusinessRevenueData();
            data.setDate(stats.getDate());
            data.setBusinessType(3); // 对战业务类型
            data.setOrderCount(stats.getBattleCount());
            data.setTotalAmount(stats.getTotalRevenue());
            data.setAvgAmount(stats.getAvgRevenue());
            result.add(data);
        }
        return result;
    }

    @Override
    public List<RechargeData> getRechargeTrend(FinancialStatsRequest request) {
        log.info("获取充值趋势数据，请求参数：{}", request);

        try {
            // 转换时间格式为时间戳（秒）
            Integer startTimestamp = convertToSecondTimestamp(request.getStartTime());
            Integer endTimestamp = convertToSecondTimestamp(request.getEndTime());

            log.debug("时间戳转换 - 开始：{}，结束：{}", startTimestamp, endTimestamp);

            // 调用Mapper查询充值数据（需要在VimOrderRechargeMapper中添加方法）
            List<RechargeData> result = getRechargeTrendFromMapper(startTimestamp, endTimestamp);

            log.info("充值趋势数据查询成功，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取充值趋势数据失败", e);
            throw new RuntimeException("获取充值趋势数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CommissionData> getCommissionTrend(FinancialStatsRequest request) {
        log.info("获取佣金趋势数据，请求参数：{}", request);

        try {
            // 转换时间格式为时间戳（秒）
            Long startTimestamp = convertToSecondTimestamp(request.getStartTime()).longValue();
            Long endTimestamp = convertToSecondTimestamp(request.getEndTime()).longValue();

            log.debug("时间戳转换 - 开始：{}，结束：{}", startTimestamp, endTimestamp);

            // 调用Mapper查询佣金数据（需要在SysOrderMapper中添加方法）
            List<CommissionData> result = getCommissionTrendFromMapper(startTimestamp, endTimestamp);

            log.info("佣金趋势数据查询成功，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取佣金趋势数据失败", e);
            throw new RuntimeException("获取佣金趋势数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<VimOrderBoxMapper.UserActivityStats> getUserActivityHeatmap(FinancialStatsRequest request) {
        log.info("获取用户活跃度热力图数据，请求参数：{}", request);

        try {
            String startTimestamp = convertToTimestamp(request.getStartTime());
            String endTimestamp = convertToTimestamp(request.getEndTime());

            List<VimOrderBoxMapper.UserActivityStats> result =
                    vimOrderBoxMapper.getUserActivityStats(startTimestamp, endTimestamp);

            log.info("用户活跃度数据查询成功，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取用户活跃度数据失败", e);
            throw new RuntimeException("获取用户活跃度数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<VimOrderBoxMapper.BoxConsumptionStats> getBoxConsumptionTrend(FinancialStatsRequest request) {
        log.info("获取开箱消费趋势数据，请求参数：{}", request);

        try {
            String startTimestamp = convertToTimestamp(request.getStartTime());
            String endTimestamp = convertToTimestamp(request.getEndTime());

            log.debug("时间戳转换 - 开始：{}，结束：{}", startTimestamp, endTimestamp);

            List<VimOrderBoxMapper.BoxConsumptionStats> result =
                    vimOrderBoxMapper.getBoxConsumptionStats(startTimestamp, endTimestamp);

            // 确保永远不返回null，如果查询结果为null则返回空列表
            if (result == null) {
                log.warn("开箱消费数据查询返回null，返回空列表");
                result = new ArrayList<>();
            }

            log.info("开箱消费数据查询成功，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取开箱消费数据失败", e);
            // 发生异常时返回空列表而不是抛出异常，避免前端显示错误
            log.warn("由于异常返回空的开箱消费数据列表");
            return new ArrayList<>();
        }
    }

    @Override
    public List<Object> getRechargeDistribution() {
        // TODO: 实现充值分布数据查询
        log.info("获取充值分布数据");
        return new ArrayList<>();
    }

    // ==================== 私有方法 ====================

    /**
     * 转换时间字符串为时间戳（毫秒）
     * 注意：数据库中的时间戳是UTC时间，需要转换为UTC时间戳
     */
    private String convertToTimestamp(String timeStr) {
        try {
            // 解析北京时间
            LocalDateTime beijingDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 转换为UTC时间戳（毫秒）
            long timestamp = beijingDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();

            log.debug("时间转换：{} (北京时间) -> {} (UTC毫秒时间戳)", timeStr, timestamp);
            return String.valueOf(timestamp);
        } catch (Exception e) {
            log.error("时间格式转换失败：{}", timeStr, e);
            throw new RuntimeException("时间格式转换失败：" + timeStr, e);
        }
    }

    /**
     * 转换时间字符串为时间戳（秒）
     * 注意：数据库中的时间戳是UTC时间，需要转换为UTC时间戳
     */
    private Integer convertToSecondTimestamp(String timeStr) {
        try {
            // 解析北京时间
            LocalDateTime beijingDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 转换为UTC时间戳（秒）
            long timestamp = beijingDateTime.toInstant(ZoneOffset.of("+8")).getEpochSecond();

            log.debug("时间转换：{} (北京时间) -> {} (UTC秒时间戳)", timeStr, timestamp);
            return (int) timestamp;
        } catch (Exception e) {
            log.error("时间格式转换失败：{}", timeStr, e);
            throw new RuntimeException("时间格式转换失败：" + timeStr, e);
        }
    }

    /**
     * 从Mapper获取充值趋势数据
     */
    private List<RechargeData> getRechargeTrendFromMapper(Integer startTimestamp, Integer endTimestamp) {
        try {
            // 调用VimOrderRechargeMapper查询充值趋势数据
            List<VimOrderRechargeMapper.RechargeTrendStats> rawData =
                    vimOrderRechargeMapper.getRechargeTrendStats(startTimestamp, endTimestamp);

            // 转换为DTO
            List<RechargeData> result = new ArrayList<>();
            for (VimOrderRechargeMapper.RechargeTrendStats stats : rawData) {
                RechargeData data = new RechargeData();
                data.setDate(stats.getDate());
                data.setRechargeCount(stats.getRechargeCount());
                data.setTotalAmount(stats.getTotalAmount());
                data.setAvgAmount(stats.getAvgAmount());
                result.add(data);
            }

            log.info("充值趋势数据查询完成，返回 {} 条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询充值趋势数据失败", e);
            throw new RuntimeException("查询充值趋势数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从Mapper获取佣金趋势数据
     */
    private List<CommissionData> getCommissionTrendFromMapper(Long startTimestamp, Long endTimestamp) {
        try {
            // 调用SysOrderMapper查询佣金趋势数据
            List<SysOrderMapper.CommissionTrendStats> rawData =
                    sysOrderMapper.getCommissionTrendStats(startTimestamp, endTimestamp);

            // 转换为DTO
            List<CommissionData> result = new ArrayList<>();
            for (SysOrderMapper.CommissionTrendStats stats : rawData) {
                CommissionData data = new CommissionData();
                data.setDate(stats.getDate());
                data.setCommissionOrders(stats.getCommissionOrders());
                data.setTotalRechargeAmount(stats.getTotalRechargeAmount());
                data.setTotalCommission(stats.getTotalCommission());
                data.setAvgCommissionRate(stats.getAvgCommissionRate());
                result.add(data);
            }

            log.info("佣金趋势数据查询完成，返回 {} 条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询佣金趋势数据失败", e);
            throw new RuntimeException("查询佣金趋势数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<VimOrderRechargeMapper.UserBehaviorStats> getUserBehaviorStats(FinancialStatsRequest request) {
        log.info("获取用户充值行为统计数据，请求参数：{}", request);

        try {
            Integer startTimestamp = convertToSecondTimestamp(request.getStartTime());
            Integer endTimestamp = convertToSecondTimestamp(request.getEndTime());

            log.debug("时间戳转换 - 开始：{}，结束：{}", startTimestamp, endTimestamp);

            List<VimOrderRechargeMapper.UserBehaviorStats> result =
                    vimOrderRechargeMapper.getUserBehaviorStats(startTimestamp, endTimestamp);

            // 确保永远不返回null，如果查询结果为null则返回空列表
            if (result == null) {
                log.warn("用户充值行为数据查询返回null，返回空列表");
                result = new ArrayList<>();
            }

            log.info("用户充值行为数据查询成功，返回 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取用户充值行为数据失败", e);
            throw new RuntimeException("获取用户充值行为数据失败：" + e.getMessage(), e);
        }
    }

}
