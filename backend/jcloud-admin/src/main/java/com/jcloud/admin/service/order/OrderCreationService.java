package com.jcloud.admin.service.order;

import com.jcloud.admin.dto.response.SubordinateProfit;
import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.enums.OrderStatus;
import com.jcloud.common.enums.SettlementStatus;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单创建服务
 * 负责订单创建相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCreationService {
    
    private final VimUserMapper vimUserMapper;
    private final OperationsMapper operationsMapper;
    private final OrderCalculationService calculationService;
    
    /**
     * 为代理用户创建订单（主订单 + 子订单）
     * 
     * @param agent 代理用户
     * @param request 创建请求
     * @return 订单列表
     */
    public List<SysOrder> createAgentOrders(VimUser agent, OrderCreateRequest request) {
        List<SysOrder> orders = new ArrayList<>();
        
        // 1. 查询代理下的所有下级用户（包括普通用户、主播用户等）
        List<VimUser> subordinateUsers = vimUserMapper.selectSubordinateUsers(agent.getId());
        log.info("代理 {} 下级用户数量: {}", agent.getId(), subordinateUsers.size());
        
        // 2. 创建主订单（代理自己的订单）
        SysOrder mainOrder = createMainOrder(agent, request);
        orders.add(mainOrder);
        
        // 3. 为每个下级用户创建子订单
        for (VimUser subordinateUser : subordinateUsers) {
            try {
                SysOrder subOrder = createSubOrder(subordinateUser, agent, request, mainOrder.getPayid());
                if (subOrder != null) {
                    orders.add(subOrder);
                }
            } catch (Exception e) {
                log.error("为下级用户 {} 创建子订单失败", subordinateUser.getId(), e);
                // 继续处理其他用户，不中断整个流程
            }
        }
        
        log.info("代理 {} 订单创建完成，主订单1个，子订单{}个", agent.getId(), orders.size() - 1);
        return orders;
    }
    
    /**
     * 为主播用户创建订单
     * 
     * @param anchor 主播用户
     * @param request 创建请求
     * @return 订单列表
     */
    public List<SysOrder> createAnchorOrders(VimUser anchor, OrderCreateRequest request) {
        List<SysOrder> orders = new ArrayList<>();
        
        // 创建独立订单 - 使用OperationsMapper获取统一数据
        SysOrder order = createIndependentOrder(anchor, request);
        orders.add(order);
        
        log.info("主播 {} 订单创建完成", anchor.getId());
        return orders;
    }
    
    /**
     * 创建主订单（代理自己的订单）
     * 
     * @param agent 代理用户
     * @param request 创建请求
     * @return 主订单
     */
    private SysOrder createMainOrder(VimUser agent, OrderCreateRequest request) {
        SysOrder order = new SysOrder();
        
        // 基本信息
        order.setPayid(calculationService.generateOrderId("MAIN"));
        order.setUserId(agent.getId().toString());
        order.setAgent(agent.getId().toString());
        order.setAnchor(null); // 主订单没有主播
        
        // 时间信息
        order.setStartTime(request.getStartTime());
        order.setEndTime(request.getEndTime());
        order.setCreateTime(TimeUtil.getCurrentTimestamp());
        
        // 计算代理自己的充值金额
        BigDecimal selfRechargeAmount = calculationService.calculateUserSelfRechargeAmount(
            agent.getId(), request);
        
        // 金额信息
        order.setTotalAmount(selfRechargeAmount);
        order.setCommissionAmount(calculationService.calculateAgentCommission(selfRechargeAmount, agent));
        order.setActualAmount(order.getCommissionAmount());
        
        // 状态信息
        order.setOrderStatus(OrderStatus.PENDING.getCode());
        order.setSettlementStatus(SettlementStatus.PENDING.getCode());
        
        // 创建人信息
        order.setCreateBy(SecurityUtils.getUserId());
        
        log.debug("创建主订单: {}", order.getPayid());
        return order;
    }
    
    /**
     * 创建子订单
     * 
     * @param subordinateUser 下级用户
     * @param agent 代理用户
     * @param request 创建请求
     * @param parentOrderId 父订单ID
     * @return 子订单
     */
    private SysOrder createSubOrder(VimUser subordinateUser, VimUser agent, 
                                   OrderCreateRequest request, String parentOrderId) {
        
        // 计算下级用户的利润数据
        SubordinateProfit profit = operationsMapper.getSubordinateProfit(
            agent.getId(), 5, // 最大层级
            request.getStartTime().toString(),
            request.getEndTime().toString()
        );
        
        // 如果没有利润数据，跳过此用户
        if (profit == null || profit.getZonglirun().compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("下级用户 {} 无利润数据，跳过创建子订单", subordinateUser.getId());
            return null;
        }
        
        SysOrder order = new SysOrder();
        
        // 基本信息
        order.setPayid(calculationService.generateOrderId("SUB"));
        order.setParentsPayid(parentOrderId);
        order.setUserId(subordinateUser.getId().toString());
        order.setAgent(agent.getId().toString());
        
        // 如果下级用户是主播，设置主播字段
        if (subordinateUser.isAnchor()) {
            order.setAnchor(subordinateUser.getId().toString());
        }
        
        // 时间信息
        order.setStartTime(request.getStartTime());
        order.setEndTime(request.getEndTime());
        order.setCreateTime(TimeUtil.getCurrentTimestamp());
        
        // 金额信息
        order.setTotalAmount(profit.getZongchongzhi());
        order.setCommissionAmount(calculationService.calculateAgentCommission(profit.getZonglirun(), agent));
        order.setActualAmount(order.getCommissionAmount());
        
        // 状态信息
        order.setOrderStatus(OrderStatus.PENDING.getCode());
        order.setSettlementStatus(SettlementStatus.PENDING.getCode());
        
        // 创建人信息
        order.setCreateBy(SecurityUtils.getUserId());
        
        log.debug("创建子订单: {} for user: {}", order.getPayid(), subordinateUser.getId());
        return order;
    }
    
    /**
     * 创建独立订单（主播独立订单）
     * 
     * @param user 用户
     * @param request 创建请求
     * @return 独立订单
     */
    private SysOrder createIndependentOrder(VimUser user, OrderCreateRequest request) {
        SysOrder order = new SysOrder();
        
        // 基本信息
        order.setPayid(calculationService.generateOrderId("IND"));
        order.setUserId(user.getId().toString());
        order.setAgent(null); // 独立订单没有代理
        
        // 如果是主播，设置主播字段
        if (user.isAnchor()) {
            order.setAnchor(user.getId().toString());
        }
        
        // 时间信息
        order.setStartTime(request.getStartTime());
        order.setEndTime(request.getEndTime());
        order.setCreateTime(TimeUtil.getCurrentTimestamp());
        
        // 计算用户自充值金额
        BigDecimal selfRechargeAmount = calculationService.calculateUserSelfRechargeAmount(
            user.getId(), request);
        
        // 金额信息
        order.setTotalAmount(selfRechargeAmount);
        order.setCommissionAmount(calculationService.calculateAnchorCommission(selfRechargeAmount, user));
        order.setActualAmount(order.getCommissionAmount());
        
        // 状态信息
        order.setOrderStatus(OrderStatus.PENDING.getCode());
        order.setSettlementStatus(SettlementStatus.PENDING.getCode());
        
        // 创建人信息
        order.setCreateBy(SecurityUtils.getUserId());
        
        log.debug("创建独立订单: {}", order.getPayid());
        return order;
    }
}
