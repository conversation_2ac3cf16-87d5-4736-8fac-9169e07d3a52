package com.jcloud.admin.service;

import com.jcloud.admin.dto.BusinessRevenueData;
import com.jcloud.admin.dto.RechargeData;
import com.jcloud.admin.dto.CommissionData;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.mapper.VimOrderBoxMapper;
import com.jcloud.common.mapper.VimOrderRechargeMapper;

import java.util.List;

/**
 * 业务数据服务接口
 * 提供基于真实数据库的业务数据查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BusinessDataService {

    /**
     * 获取业务收入趋势数据
     * 基于vim_order_box表的真实数据
     *
     * @param request 查询请求参数
     * @return 业务收入趋势数据列表
     */
    List<BusinessRevenueData> getBusinessRevenueTrend(FinancialStatsRequest request);

    /**
     * 获取充值趋势数据
     * 基于vim_order_recharge表的真实数据
     *
     * @param request 查询请求参数
     * @return 充值趋势数据列表
     */
    List<RechargeData> getRechargeTrend(FinancialStatsRequest request);

    /**
     * 获取佣金趋势数据
     * 基于sys_order表的真实数据
     *
     * @param request 查询请求参数
     * @return 佣金趋势数据列表
     */
    List<CommissionData> getCommissionTrend(FinancialStatsRequest request);

    /**
     * 获取用户活跃度热力图数据
     *
     * @param request 查询请求参数
     * @return 用户活跃度数据列表
     */
    List<VimOrderBoxMapper.UserActivityStats> getUserActivityHeatmap(FinancialStatsRequest request);

    /**
     * 获取开箱消费趋势数据
     *
     * @param request 查询请求参数
     * @return 开箱消费数据列表
     */
    List<VimOrderBoxMapper.BoxConsumptionStats> getBoxConsumptionTrend(FinancialStatsRequest request);


    /**
     * 获取充值分布数据
     *
     * @return 充值分布数据列表
     */
    List<Object> getRechargeDistribution();

    /**
     * 获取用户充值行为统计数据
     *
     * @param request 查询请求参数
     * @return 用户充值行为统计数据列表
     */
    List<VimOrderRechargeMapper.UserBehaviorStats> getUserBehaviorStats(FinancialStatsRequest request);

}
