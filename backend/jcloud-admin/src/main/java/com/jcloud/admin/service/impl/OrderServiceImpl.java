package com.jcloud.admin.service.impl;

import com.jcloud.admin.dto.response.SubordinateProfit;
import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.admin.service.OrderService;
import com.jcloud.common.dto.ActualSettlementItem;
import com.jcloud.common.dto.BatchSettleRequest;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.entity.VimOrderRecharge;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.enums.OrderStatus;
import com.jcloud.common.enums.SettlementStatus;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.mapper.VimOrderRechargeMapper;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.vo.OrderVO;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 佣金结算订单服务实现类
 *
 * @deprecated 此类已被重构拆分为多个职责单一的服务类，请使用 RefactoredOrderService
 * @see com.jcloud.admin.service.order.RefactoredOrderService
 * @see com.jcloud.admin.service.order.OrderValidationService
 * @see com.jcloud.admin.service.order.OrderCalculationService
 * @see com.jcloud.admin.service.order.OrderCreationService
 * @see com.jcloud.admin.service.order.OrderQueryService
 * @see com.jcloud.admin.service.order.OrderSettlementService
 * @see com.jcloud.admin.service.order.OrderExportService
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Deprecated
@Slf4j
@Service("legacyOrderService")
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {

    private final SysOrderMapper sysOrderMapper;
    private final VimUserMapper vimUserMapper;
    private final VimOrderRechargeMapper vimOrderRechargeMapper;
    private final OperationsMapper operationsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SysOrder> createOrders(OrderCreateRequest request) {
        // 获取当前登录用户ID（用于权限验证）
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取目标用户ID（要为哪个用户创建订单）
        Long targetUserId = request.getTargetUserId();
        if (targetUserId == null) {
            throw new BusinessException("目标用户ID不能为空");
        }

        log.info("开始创建佣金结算订单，当前用户ID: {}, 目标用户ID: {}, 请求: {}", currentUserId, targetUserId, request);

        // 1. 参数验证
        validateCreateRequest(request);
        // 2. 查询目标用户信息（使用目标用户ID）
        VimUser targetUser = vimUserMapper.selectByUserId(targetUserId.intValue());
        if (targetUser == null || !targetUser.isActive()) {
            throw new BusinessException("目标用户不存在或已禁用");
        }

        // 3. 检查是否已有重复订单（使用目标用户ID）
        if (hasExistingOrder(targetUserId.toString(), request.getStartTime(), request.getEndTime())) {
            throw new BusinessException("该时间段已存在结算订单，请勿重复创建");
        }
        // 4. 根据目标用户身份创建订单
        List<SysOrder> orders;
        if (targetUser.isAgent()) {
            // 代理用户：创建主订单 + 子订单
            orders = createAgentOrders(targetUser, request);
        } else if (targetUser.isAnchor()) {
            // 主播用户：创建单个订单
            orders = createAnchorOrders(targetUser, request);
        } else {
            throw new BusinessException("目标用户不是代理或主播，无法创建结算订单");
        }

        // 5. 批量插入订单
        if (!CollectionUtils.isEmpty(orders)) {
            int insertCount = sysOrderMapper.insertBatch(orders);
            log.info("批量插入订单完成，插入数量: {}", insertCount);
        }

        log.info("佣金结算订单创建完成，共创建 {} 个订单", orders.size());
        return orders;
    }

    @Override
    public PageResult<OrderVO> pageOrders(OrderQueryRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("分页查询订单列表，用户ID: {}, 请求: {}", currentUserId, request);

        // 构建查询条件（自动过滤当前用户的数据）
        QueryWrapper queryWrapper = buildQueryWrapper(request, currentUserId.toString());

        // 只查询主订单（没有父订单的订单）
        queryWrapper.isNull("parents_payid");

        // 分页查询主订单
        Page<SysOrder> page = Page.of(request.getPageNum(), request.getPageSize());
        Page<SysOrder> result = sysOrderMapper.paginate(page, queryWrapper);

        // 批量转换为VO（优化用户昵称查询）
        List<OrderVO> orderVOs = batchConvertToOrderVOs(result.getRecords());

        return PageResult.of(orderVOs, result.getTotalRow(), request.getPageNum(), request.getPageSize());
    }

    @Override
    public OrderVO getOrderDetail(String payid) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }
        log.info("查询订单详情，订单ID: {}, 用户ID: {}", payid, currentUserId);
        // 查询订单
        SysOrder order = sysOrderMapper.selectOneById(payid);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        // 数据权限检查：管理员可以查看所有订单，普通用户只能查看自己的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin() && isUserOrder(order, currentUserId.toString())) {
            throw new BusinessException("无权限访问该订单");
        }
        // 转换为VO
        return convertToOrderVO(order);
    }

    @Override
    public OrderVO getMainOrderWithSubOrders(String parentsPayid) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("查询主订单及子订单，主订单ID: {}, 用户ID: {}", parentsPayid, currentUserId);

        // 查询主订单
        SysOrder mainOrder = sysOrderMapper.selectOneById(parentsPayid);
        if (mainOrder == null || !mainOrder.isMainOrder()) {
            throw new BusinessException("主订单不存在");
        }

        // 数据权限检查：管理员可以查看所有订单，普通用户只能查看自己的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin() && isUserOrder(mainOrder, currentUserId.toString())) {
            throw new BusinessException("无权限访问该订单");
        }

        // 查询子订单
        List<SysOrder> subOrders = sysOrderMapper.selectSubOrdersByParentId(parentsPayid);

        // 批量转换为VO
        OrderVO mainOrderVO = convertToOrderVO(mainOrder);
        List<OrderVO> subOrderVOs = batchConvertToOrderVOs(subOrders);
        mainOrderVO.setSubOrders(subOrderVOs);

        return mainOrderVO;
    }

    @Override
    public List<OrderVO> getSubOrdersByMainOrderId(String mainOrderId) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("查询主订单的子订单，主订单ID: {}, 用户ID: {}", mainOrderId, currentUserId);

        // 验证主订单是否存在且有权限访问
        SysOrder mainOrder = sysOrderMapper.selectOneById(mainOrderId);
        if (mainOrder == null || !mainOrder.isMainOrder()) {
            throw new BusinessException("主订单不存在");
        }

        // 数据权限检查：管理员可以查看所有订单，普通用户只能查看自己的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin() && isUserOrder(mainOrder, currentUserId.toString())) {
            throw new BusinessException("无权限访问该订单");
        }

        // 查询子订单
        QueryWrapper subOrderQuery = QueryWrapper.create()
                .eq("parents_payid", mainOrderId);

        // 添加权限过滤
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            subOrderQuery.and("(agent = '" + currentUserId + "' OR anchor = '" + currentUserId + "')");
        }

        List<SysOrder> subOrders = sysOrderMapper.selectListByQuery(subOrderQuery);

        return batchConvertToOrderVOs(subOrders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int settleOrders(List<String> payids) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("批量结算订单，订单数量: {}, 用户ID: {}", payids.size(), currentUserId);

        if (CollectionUtils.isEmpty(payids)) {
            throw new BusinessException("订单ID列表不能为空");
        }

        // 数据权限检查：批量验证订单归属
        validateOrdersOwnership(payids, currentUserId.toString());

        // 批量更新状态
        int updateCount = sysOrderMapper.updateStatusBatch(
                payids,
                OrderStatus.SETTLED.getCode(),
                SettlementStatus.SUCCESS.getCode()
        );

        log.info("批量结算完成，更新订单数量: {}", updateCount);
        return updateCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int settleOrdersWithActualAmount(BatchSettleRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        List<ActualSettlementItem> items = request.getItems();
        log.info("批量结算订单（带实际结算金额），订单数量: {}, 用户ID: {}", items.size(), currentUserId);

        if (CollectionUtils.isEmpty(items)) {
            throw new BusinessException("结算项列表不能为空");
        }

        // 提取订单ID列表
        List<String> payids = items.stream()
                .map(ActualSettlementItem::getPayid)
                .collect(Collectors.toList());

        // 数据权限检查：批量验证订单归属
        validateOrdersOwnership(payids, currentUserId.toString());

        // 检查是否有已结算的订单
        List<SysOrder> existingOrders = sysOrderMapper.selectListByQuery(
                QueryWrapper.create().where(SysOrder::getPayid).in(payids)
        );
        List<String> alreadySettledOrders = existingOrders.stream()
                .filter(order -> SettlementStatus.SUCCESS.getCode().equals(order.getSettlementStatus()))
                .map(SysOrder::getPayid)
                .collect(Collectors.toList());

        if (!alreadySettledOrders.isEmpty()) {
            log.warn("发现已结算的订单，将跳过: {}", alreadySettledOrders);
            // 从结算项中移除已结算的订单
            items = items.stream()
                    .filter(item -> !alreadySettledOrders.contains(item.getPayid()))
                    .toList();

            if (items.isEmpty()) {
                log.warn("所有订单均已结算，无需重复处理");
                return 0;
            }
        }

        // 批量更新订单状态和实际结算金额
        int updateCount = 0;
        for (ActualSettlementItem item : items) {
            // 更新主订单的状态和实际结算金额
            int count = sysOrderMapper.updateOrderWithActualAmount(
                    item.getPayid(),
                    item.getActualAmount(),
                    OrderStatus.SETTLED.getCode(),
                    SettlementStatus.SUCCESS.getCode()
            );
            updateCount += count;

            // 如果是主订单，同时更新所有子订单的状态（子订单跟随主订单状态）
            List<String> subOrderIds = sysOrderMapper.findSubOrderIdsByParentPayid(item.getPayid());
            if (!subOrderIds.isEmpty()) {
                int subOrderUpdateCount = sysOrderMapper.updateStatusBatch(
                        subOrderIds,
                        OrderStatus.SETTLED.getCode(),
                        SettlementStatus.SUCCESS.getCode()
                );
                updateCount += subOrderUpdateCount;
                log.debug("主订单 {} 的 {} 个子订单状态已更新", item.getPayid(), subOrderUpdateCount);
            }

            log.debug("更新订单 {} 的实际结算金额为 {}", item.getPayid(), item.getActualAmount());
        }

        log.info("批量结算（带实际结算金额）完成，更新订单数量: {}", updateCount);
        return updateCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String payid) {
        return cancelOrders(Collections.singletonList(payid)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelOrders(List<String> payids) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("批量取消订单，订单数量: {}, 用户ID: {}", payids.size(), currentUserId);

        if (CollectionUtils.isEmpty(payids)) {
            throw new BusinessException("订单ID列表不能为空");
        }

        // 数据权限检查：批量验证订单归属
        validateOrdersOwnership(payids, currentUserId.toString());

        // 批量更新状态
        int updateCount = sysOrderMapper.updateStatusBatch(
                payids,
                OrderStatus.CANCELLED.getCode(),
                SettlementStatus.NONE.getCode()
        );

        log.info("批量取消完成，更新订单数量: {}", updateCount);
        return updateCount;
    }

    @Override
    public boolean canCreateOrder(OrderCreateRequest request) {
        try {
            // 获取当前登录用户ID（用于权限验证）
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId == null) {
                return false;
            }

            // 获取目标用户ID
            Long targetUserId = request.getTargetUserId();
            if (targetUserId == null) {
                return false;
            }

            // 验证目标用户是否存在且有效
            VimUser targetUser = vimUserMapper.selectByUserId(targetUserId.intValue());
            if (targetUser == null || !targetUser.isActive()) {
                return false;
            }

            // 验证目标用户是否为代理或主播
            if (!targetUser.isAgent() && !targetUser.isAnchor()) {
                return false;
            }

            validateCreateRequest(request);
            return !hasExistingOrder(targetUserId.toString(), request.getStartTime(), request.getEndTime());
        } catch (Exception e) {
            log.warn("检查创建订单权限失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public OrderStatistics getOrderStatistics() {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("获取用户订单统计，用户ID: {}", currentUserId);

        QueryWrapper queryWrapper = QueryWrapper.create()
                .and("(agent = '" + currentUserId.toString() + "' OR anchor = '" + currentUserId.toString() + "')");

        List<SysOrder> orders = sysOrderMapper.selectListByQuery(queryWrapper);

        OrderStatistics statistics = new OrderStatistics();
        statistics.setTotalOrders((long) orders.size());
        statistics.setPendingOrders(orders.stream()
                .filter(o -> OrderStatus.PENDING_SETTLEMENT.getCode().equals(o.getOrderStatus()))
                .count());
        statistics.setSettledOrders(orders.stream()
                .filter(o -> OrderStatus.SETTLED.getCode().equals(o.getOrderStatus()))
                .count());
        statistics.setCancelledOrders(orders.stream()
                .filter(o -> OrderStatus.CANCELLED.getCode().equals(o.getOrderStatus()))
                .count());

        return statistics;
    }

    @Override
    public Map<String, Object> getOrderFinancialStats(Integer startTime, Integer endTime) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("获取订单财务统计，用户ID: {}, 时间范围: {} - {}", currentUserId, startTime, endTime);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取当前用户的vim_user信息
            VimUser currentUser = vimUserMapper.selectByUserId(currentUserId.intValue());
            if (currentUser == null) {
                throw new BusinessException("用户信息不存在");
            }

            // 设置默认时间范围（如果未提供）
            if (startTime == null || endTime == null) {
                // 默认查询最近30天
                long now = System.currentTimeMillis() / 1000;
                endTime = (int) now;
                startTime = (int) (now - 30 * 24 * 60 * 60); // 30天前
            }

            // 根据用户身份确定查询层级
            Integer maxLevel = currentUser.isAgent() ? 2 : 1;
            String startTimeStr = startTime.toString();
            String endTimeStr = endTime.toString();

            // 调用OperationsMapper获取统一的财务统计数据
            SubordinateProfit operationsStats = operationsMapper.getSubordinateProfit(
                    currentUser.getId(), maxLevel, startTimeStr, endTimeStr);

            // 构建返回数据
            result.put("totalRecharge", operationsStats.getZongchongzhi() != null ? operationsStats.getZongchongzhi() : BigDecimal.ZERO);
            result.put("totalProfit", operationsStats.getZonglirun() != null ? operationsStats.getZonglirun() : BigDecimal.ZERO);
            result.put("userIdentity", currentUser.getIdentity());
            result.put("userLevel", maxLevel);
            result.put("startTime", startTime);
            result.put("endTime", endTime);

            log.info("✅ 财务统计查询成功 - 用户: {}, 身份: {}, 层级: {}, 总充值: {}, 总利润: {}",
                    currentUser.getId(), currentUser.getIdentity(), maxLevel,
                    operationsStats.getZongchongzhi(), operationsStats.getZonglirun());

        } catch (Exception e) {
            log.error("❌ 获取财务统计失败: {}", e.getMessage(), e);

            // 返回默认值
            result.put("totalRecharge", BigDecimal.ZERO);
            result.put("totalProfit", BigDecimal.ZERO);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public List<OrderVO> exportOrders(OrderQueryRequest request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId == null) {
            throw new BusinessException("用户未登录");
        }

        log.info("导出订单数据，用户ID: {}", currentUserId);

        // 构建查询条件（不分页）
        QueryWrapper queryWrapper = buildQueryWrapper(request, currentUserId.toString());
        List<SysOrder> orders = sysOrderMapper.selectListByQuery(queryWrapper);

        return batchConvertToOrderVOs(orders);
    }

    @Override
    public byte[] exportOrdersToExcel(OrderQueryRequest request) {
        // 获取订单数据
        List<OrderVO> orders = exportOrders(request);

        // 获取所有相关用户信息（代理和主播）
        Map<String, VimUser> userInfoMap = getUserInfoMap(orders);

        try {
            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("代理结算");

            // 重新设计分组逻辑：按主订单分组，每组包含主订单和其子订单
            Map<String, OrderGroup> orderGroups = buildOrderGroups(orders, userInfoMap);

            int currentRow = 0;

            log.info("开始生成Excel文件，共有 {} 个订单组", orderGroups.size());

            // 创建增强的Excel样式
            ExcelStyles styles = createExcelStyles(workbook);

            // 设置列宽
            setColumnWidths(sheet);

            for (Map.Entry<String, OrderGroup> entry : orderGroups.entrySet()) {
                OrderGroup orderGroup = entry.getValue();

                log.info("处理订单组: {}, 主订单: {}, 子订单数量: {}",
                        orderGroup.getGroupKey(), orderGroup.getMainOrder().getPayid(), orderGroup.getSubOrders().size());

                // 第一行：分组标题（代理/主播信息 + 主订单编号）
                Row headerRow = sheet.createRow(currentRow++);
                Cell headerCell = headerRow.createCell(0);
                headerCell.setCellValue(orderGroup.getGroupKey());
                headerCell.setCellStyle(styles.headerStyle);
                // 合并单元格（移除状态列后，列数从13减少到12，索引从0到12）
                sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(currentRow - 1, currentRow - 1, 0, 12));

                // 第二行：表头
                Row titleRow = sheet.createRow(currentRow++);
                String[] headers = {"订单类型", "结算时间", "编号", "结账时间", "昵称", "id", "自充值", "推广充值", "劳务比例", "推广劳务费", "实际结算", "收款人", "收款账户"};
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = titleRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(styles.titleStyle);
                }

                // 数据行：先填充主订单，再填充子订单
                currentRow = fillOrderData(sheet, orderGroup.getMainOrder(), currentRow, styles, userInfoMap);

                // 填充子订单
                for (OrderVO subOrder : orderGroup.getSubOrders()) {
                    currentRow = fillOrderData(sheet, subOrder, currentRow, styles, userInfoMap);
                }

                // 添加合计行
                currentRow = addSummaryRow(sheet, orderGroup, currentRow, styles);

                // 添加空行分隔不同订单组
                currentRow++;
            }

            // 输出Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            byte[] excelData = outputStream.toByteArray();
            outputStream.close();

            log.info("Excel文件生成成功，文件大小: {} bytes, 总行数: {}", excelData.length, currentRow);

            return excelData;

        } catch (IOException e) {
            log.error("生成Excel文件失败", e);
            throw new BusinessException("生成Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * Excel样式集合类
     */
    private static class ExcelStyles {
        public CellStyle headerStyle;        // 分组标题样式（代理信息）
        public CellStyle titleStyle;         // 表头样式（订单信息）
        public CellStyle dataStyle;          // 数据行样式（订单明细）
        public CellStyle summaryStyle;       // 合计行样式
        public CellStyle amountStyle;        // 金额样式
        public CellStyle percentStyle;       // 百分比样式
        public CellStyle summaryAmountStyle; // 合计行金额样式
    }

    /**
     * 创建Excel样式
     */
    private ExcelStyles createExcelStyles(Workbook workbook) {
        ExcelStyles styles = new ExcelStyles();

        // 1. 分组标题样式（代理信息表头：浅绿色背景 + 黑色文字）
        styles.headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        styles.headerStyle.setFont(headerFont);
        styles.headerStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        styles.headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.headerStyle.setAlignment(HorizontalAlignment.LEFT);
        styles.headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(styles.headerStyle);

        // 2. 表头样式（订单信息表头：香蕉色背景 + 黑色文字）
        styles.titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 10);
        titleFont.setColor(IndexedColors.BLACK.getIndex());
        styles.titleStyle.setFont(titleFont);
        styles.titleStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        styles.titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.titleStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(styles.titleStyle);

        // 3. 数据行样式（订单明细行：无背景色 + 黑色文字）
        styles.dataStyle = workbook.createCellStyle();
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 10);
        dataFont.setColor(IndexedColors.BLACK.getIndex());
        styles.dataStyle.setFont(dataFont);
        styles.dataStyle.setAlignment(HorizontalAlignment.LEFT);
        styles.dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(styles.dataStyle);

        // 4. 合计行样式（加粗红色文字 + 浅红色背景）
        styles.summaryStyle = workbook.createCellStyle();
        Font summaryFont = workbook.createFont();
        summaryFont.setBold(true);
        summaryFont.setFontHeightInPoints((short) 10);
        summaryFont.setColor(IndexedColors.RED.getIndex());
        styles.summaryStyle.setFont(summaryFont);
        styles.summaryStyle.setFillForegroundColor(IndexedColors.ROSE.getIndex());
        styles.summaryStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.summaryStyle.setAlignment(HorizontalAlignment.LEFT);
        styles.summaryStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(styles.summaryStyle);

        // 5. 金额样式（数据行金额）
        styles.amountStyle = workbook.createCellStyle();
        styles.amountStyle.cloneStyleFrom(styles.dataStyle);
        styles.amountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        styles.amountStyle.setAlignment(HorizontalAlignment.RIGHT);

        // 6. 百分比样式
        styles.percentStyle = workbook.createCellStyle();
        styles.percentStyle.cloneStyleFrom(styles.dataStyle);
        styles.percentStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
        styles.percentStyle.setAlignment(HorizontalAlignment.RIGHT);

        // 7. 合计行金额样式
        styles.summaryAmountStyle = workbook.createCellStyle();
        styles.summaryAmountStyle.cloneStyleFrom(styles.summaryStyle);
        styles.summaryAmountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        styles.summaryAmountStyle.setAlignment(HorizontalAlignment.RIGHT);

        return styles;
    }

    /**
     * 设置单元格边框
     */
    private void setBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 3000);   // 订单类型
        sheet.setColumnWidth(1, 5000);   // 结算时间
        sheet.setColumnWidth(2, 6000);   // 编号
        sheet.setColumnWidth(3, 4000);   // 结账时间
        sheet.setColumnWidth(4, 3000);   // 昵称
        sheet.setColumnWidth(5, 2000);   // id
        sheet.setColumnWidth(6, 3000);   // 自充值
        sheet.setColumnWidth(7, 3000);   // 推广充值
        sheet.setColumnWidth(8, 2500);   // 劳务比例
        sheet.setColumnWidth(9, 3000);   // 推广劳务费
        sheet.setColumnWidth(10, 3000);  // 实际结算
        sheet.setColumnWidth(11, 3000);  // 收款人
        sheet.setColumnWidth(12, 4000);  // 收款账户
    }

    /**
     * 填充订单数据行
     */
    private int fillOrderData(Sheet sheet, OrderVO order, int currentRow, ExcelStyles styles,
                              Map<String, VimUser> userInfoMap) {
        Row dataRow = sheet.createRow(currentRow);
        int colIndex = 0;

        // 统一使用数据行样式
        CellStyle rowStyle = styles.dataStyle;
        CellStyle amountStyle = styles.amountStyle;

        // 0. 订单类型
        Cell typeCell = dataRow.createCell(colIndex++);
        boolean isMainOrder = order.getIsMainOrder() != null && order.getIsMainOrder();
        typeCell.setCellValue(isMainOrder ? "主订单" : "  └子订单");
        typeCell.setCellStyle(rowStyle);

        // 1. 结算时间
        Cell timeCell = dataRow.createCell(colIndex++);
        String settlementTime = "";
        if (order.getSettlementStartTime() != null && order.getSettlementEndTime() != null) {
            settlementTime = formatTimestamp(order.getSettlementStartTime()) + " - " + formatTimestamp(order.getSettlementEndTime());
        }
        timeCell.setCellValue(settlementTime);
        timeCell.setCellStyle(rowStyle);

        // 2. 编号
        Cell payidCell = dataRow.createCell(colIndex++);
        payidCell.setCellValue(order.getPayid() != null ? order.getPayid() : "");
        payidCell.setCellStyle(rowStyle);

        // 3. 结账时间（实际给用户付款的时间）- 只有主订单显示
        Cell settlementTimeCell = dataRow.createCell(colIndex++);
        String settlementTimeValue = isMainOrder ? getSettlementTimeDisplay(order) : "";
        settlementTimeCell.setCellValue(settlementTimeValue);
        settlementTimeCell.setCellStyle(rowStyle);

        // 4. 昵称
        Cell nicknameCell = dataRow.createCell(colIndex++);
        String nickname = getUserNickname(order, userInfoMap);
        nicknameCell.setCellValue(nickname);
        nicknameCell.setCellStyle(rowStyle);

        // 5. id
        Cell userIdCell = dataRow.createCell(colIndex++);
        String userId = order.getAnchor() != null ? order.getAnchor() :
                (order.getAgent() != null ? order.getAgent() : "");
        userIdCell.setCellValue(userId);
        userIdCell.setCellStyle(rowStyle);

        // 6. 自充值
        Cell selfRechargeCell = dataRow.createCell(colIndex++);
        BigDecimal selfRecharge = order.getAmountSelf() != null ? order.getAmountSelf() : BigDecimal.ZERO;
        selfRechargeCell.setCellValue(selfRecharge.doubleValue());
        selfRechargeCell.setCellStyle(amountStyle);

        // 7. 推广充值
        Cell fansRechargeCell = dataRow.createCell(colIndex++);
        BigDecimal fansRecharge = order.getAmountFans() != null ? order.getAmountFans() : BigDecimal.ZERO;
        fansRechargeCell.setCellValue(fansRecharge.doubleValue());
        fansRechargeCell.setCellStyle(amountStyle);

        // 8. 劳务比例
        Cell feeRateCell = dataRow.createCell(colIndex++);
        if (order.getFeeRate() != null) {
            feeRateCell.setCellValue(order.getFeeRate().doubleValue() / 100.0); // 转换为小数
            feeRateCell.setCellStyle(styles.percentStyle);
        } else {
            feeRateCell.setCellValue("");
            feeRateCell.setCellStyle(rowStyle);
        }

        // 9. 推广劳务费
        Cell feeValueCell = dataRow.createCell(colIndex++);
        BigDecimal feeValue = order.getFeeValue() != null ? order.getFeeValue() : BigDecimal.ZERO;
        feeValueCell.setCellValue(feeValue.doubleValue());
        feeValueCell.setCellStyle(amountStyle);

        // 10. 实际结算
        Cell feeActualCell = dataRow.createCell(colIndex++);
        BigDecimal feeActual = order.getFeeActual() != null ? order.getFeeActual() : BigDecimal.ZERO;
        feeActualCell.setCellValue(feeActual.doubleValue());
        feeActualCell.setCellStyle(amountStyle);

        // 11. 收款人
        Cell feePersonCell = dataRow.createCell(colIndex++);
        feePersonCell.setCellValue(order.getFeePerson() != null ? order.getFeePerson() : "");
        feePersonCell.setCellStyle(rowStyle);

        // 12. 收款账户
        Cell feeAccountCell = dataRow.createCell(colIndex++);
        feeAccountCell.setCellValue(order.getFeeAccount() != null ? order.getFeeAccount() : "");
        feeAccountCell.setCellStyle(rowStyle);

        return currentRow + 1;
    }

    /**
     * 获取结账时间显示文本
     * 只有已结算的订单才显示实际结账时间，其他状态显示相应提示
     */
    private String getSettlementTimeDisplay(OrderVO order) {
        // 检查订单状态
        if (order.getOrderStatus() == null) {
            return "状态未知";
        }

        // 只有已结算的订单才显示结账时间
        if (OrderStatus.SETTLED.getCode().equals(order.getOrderStatus())) {
            // 使用更新时间作为实际结账时间（订单状态变为已结算时的时间）
            if (order.getUpdateTime() != null) {
                return formatTimestamp(order.getUpdateTime());
            } else {
                return "结算时间未记录";
            }
        } else {
            // 根据不同状态显示相应提示
            OrderStatus status = OrderStatus.fromCode(order.getOrderStatus());
            return switch (status) {
                case CREATED -> "未开始结算";
                case CALCULATED -> "等待结算";
                case PENDING_SETTLEMENT -> "结算处理中";
                case CANCELLED -> "已取消";
                default -> "未结算";
            };
        }
    }

    /**
     * 获取用户昵称
     */
    private String getUserNickname(OrderVO order, Map<String, VimUser> userInfoMap) {
        if (order.getAnchor() != null) {
            VimUser anchorUser = userInfoMap.get(order.getAnchor());
            if (anchorUser != null) {
                // 根据用户身份显示不同的前缀
                String prefix = switch (anchorUser.getIdentity()) {
                    case 1 -> "普通用户";
                    case 2 -> "线上主播";
                    case 3 -> "线下主播";
                    case 4 -> "代理";
                    default -> "用户";
                };
                return anchorUser.getNickname() + "(" + prefix + ")";
            } else {
                return "用户-" + order.getAnchor();
            }
        } else if (order.getAgent() != null) {
            VimUser agentUser = userInfoMap.get(order.getAgent());
            return agentUser != null ? agentUser.getNickname() + "(代理)" : "代理-" + order.getAgent();
        }
        return "未知用户";
    }

    /**
     * 添加合计行
     */
    private int addSummaryRow(Sheet sheet, OrderGroup orderGroup, int currentRow, ExcelStyles styles) {
        Row summaryRow = sheet.createRow(currentRow);
        int colIndex = 0;
        // 合计计算：区分不同字段的计算逻辑
        OrderVO mainOrder = orderGroup.getMainOrder();

        // 1. 自充值合计：需要累加主订单和所有子订单的自充值
        BigDecimal totalSelfAmount = mainOrder.getAmountSelf() != null ? mainOrder.getAmountSelf() : BigDecimal.ZERO;
        for (OrderVO subOrder : orderGroup.getSubOrders()) {
            totalSelfAmount = totalSelfAmount.add(subOrder.getAmountSelf() != null ? subOrder.getAmountSelf() : BigDecimal.ZERO);
        }

        // 2. 总充值金额：直接使用主订单的amount_fans（已包含统一计算的总充值）
        BigDecimal totalFansAmount = mainOrder.getAmountFans() != null ? mainOrder.getAmountFans() : BigDecimal.ZERO;

        // 3. 佣金总额：直接使用主订单的佣金（已基于统一的总充值计算）
        BigDecimal totalFeeValue = mainOrder.getFeeValue() != null ? mainOrder.getFeeValue() : BigDecimal.ZERO;

        BigDecimal totalActualSettlement = BigDecimal.ZERO;   // 实际结算合计（用户输入）

        log.info("✅ 合计计算完成 - 自充值合计: {} (主订单: {} + 子订单累加), 总充值: {}, 佣金总额: {}",
                totalSelfAmount, mainOrder.getAmountSelf(), totalFansAmount, totalFeeValue);

        // 不再需要复杂的计算逻辑，直接使用主订单的数据

        // 实际结算金额：直接使用主订单的数据，避免重复计算
        totalActualSettlement = (mainOrder.getActualSettlementAmount() != null && mainOrder.getActualSettlementAmount().compareTo(BigDecimal.ZERO) > 0)
                ? mainOrder.getActualSettlementAmount()
                : (mainOrder.getFeeActual() != null ? mainOrder.getFeeActual() : totalFeeValue);

        log.info("✅ 实际结算金额使用主订单数据: {}", totalActualSettlement);

        // 0. 订单类型
        Cell typeCell = summaryRow.createCell(colIndex++);
        typeCell.setCellValue("【合计】");
        typeCell.setCellStyle(styles.summaryStyle);

        // 1-5. 空列（结算时间、编号、结账时间、昵称、id）
        for (int i = 0; i < 5; i++) {
            Cell emptyCell = summaryRow.createCell(colIndex++);
            emptyCell.setCellValue("");
            emptyCell.setCellStyle(styles.summaryStyle);
        }

        // 6. 自充值合计
        Cell selfAmountCell = summaryRow.createCell(colIndex++);
        selfAmountCell.setCellValue(totalSelfAmount.doubleValue());
        selfAmountCell.setCellStyle(styles.summaryAmountStyle);

        // 7. 推广充值合计
        Cell fansAmountCell = summaryRow.createCell(colIndex++);
        fansAmountCell.setCellValue(totalFansAmount.doubleValue());
        fansAmountCell.setCellStyle(styles.summaryAmountStyle);

        // 8. 劳务比例（空列）
        Cell emptyRateCell = summaryRow.createCell(colIndex++);
        emptyRateCell.setCellValue("");
        emptyRateCell.setCellStyle(styles.summaryStyle);

        // 9. 推广劳务费合计
        Cell feeValueCell = summaryRow.createCell(colIndex++);
        feeValueCell.setCellValue(totalFeeValue.doubleValue());
        feeValueCell.setCellStyle(styles.summaryAmountStyle);

        // 10. 实际结算合计
        Cell feeActualCell = summaryRow.createCell(colIndex++);
        feeActualCell.setCellValue(totalActualSettlement.doubleValue());
        feeActualCell.setCellStyle(styles.summaryAmountStyle);

        // 11-12. 空列（收款人、收款账户）
        for (int i = 0; i < 2; i++) {
            Cell emptyCell = summaryRow.createCell(colIndex++);
            emptyCell.setCellValue("");
            emptyCell.setCellStyle(styles.summaryStyle);
        }

        return currentRow + 1;
    }

    /**
     * 创建子订单金额样式
     */
    private CellStyle createSubOrderAmountStyle(CellStyle baseStyle, Workbook workbook) {
        CellStyle amountStyle = workbook.createCellStyle();
        amountStyle.cloneStyleFrom(baseStyle);
        amountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        amountStyle.setAlignment(HorizontalAlignment.RIGHT);
        return amountStyle;
    }

    /**
     * 订单分组类 - 包含主订单和其子订单
     */
    @Getter
    private static class OrderGroup {
        private final OrderVO mainOrder;
        private final List<OrderVO> subOrders;
        private final String groupKey;

        public OrderGroup(OrderVO mainOrder, String groupKey) {
            this.mainOrder = mainOrder;
            this.subOrders = new ArrayList<>();
            this.groupKey = groupKey;
        }

        public void addSubOrder(OrderVO subOrder) {
            this.subOrders.add(subOrder);
        }

        public List<OrderVO> getAllOrders() {
            List<OrderVO> allOrders = new ArrayList<>();
            allOrders.add(mainOrder);
            allOrders.addAll(subOrders);
            return allOrders;
        }
    }

    /**
     * 构建订单分组 - 按主订单和子订单分组
     *
     * @param orders      订单列表
     * @param userInfoMap 用户信息映射
     * @return 分组后的订单数据
     */
    private Map<String, OrderGroup> buildOrderGroups(List<OrderVO> orders, Map<String, VimUser> userInfoMap) {
        Map<String, OrderGroup> orderGroups = new LinkedHashMap<>();
        Map<String, OrderVO> mainOrderMap = new HashMap<>();

        // 第一步：找出所有主订单
        for (OrderVO order : orders) {
            if (order.getIsMainOrder() != null && order.getIsMainOrder()) {
                VimUser agentUser = userInfoMap.get(order.getAgent());
                String agentName = agentUser != null ? agentUser.getNickname() : "无代理";
                String agentPhone = agentUser != null ? agentUser.getPhone() : "";
                String groupKey = agentName + (agentPhone != null && !agentPhone.isEmpty() ? " " + agentPhone : "")
                        + " - " + order.getPayid();

                OrderGroup group = new OrderGroup(order, groupKey);
                orderGroups.put(order.getPayid(), group);
                mainOrderMap.put(order.getPayid(), order);
            }
        }
        // 第二步：将子订单分配到对应的主订单组
        for (OrderVO order : orders) {
            if (order.getIsMainOrder() == null || !order.getIsMainOrder()) {
                String parentPayid = order.getParentsPayid();
                if (parentPayid != null && orderGroups.containsKey(parentPayid)) {
                    orderGroups.get(parentPayid).addSubOrder(order);
                } else {
                    // 如果是独立订单（没有父订单），创建单独的组
                    VimUser user = null;
                    String userName = "未知用户";
                    String userPhone = "";

                    if (order.getAgent() != null) {
                        user = userInfoMap.get(order.getAgent());
                        userName = user != null ? user.getNickname() : "代理-" + order.getAgent();
                    } else if (order.getAnchor() != null) {
                        user = userInfoMap.get(order.getAnchor());
                        userName = user != null ? user.getNickname() : "主播-" + order.getAnchor();
                    }

                    if (user != null && user.getPhone() != null) {
                        userPhone = user.getPhone();
                    }

                    String groupKey = userName + (userPhone.isEmpty() ? "" : " " + userPhone)
                            + " - " + order.getPayid();
                    OrderGroup group = new OrderGroup(order, groupKey);
                    orderGroups.put(order.getPayid(), group);
                }
            }
        }

        return orderGroups;
    }

    /**
     * 获取订单相关的用户信息映射
     *
     * @param orders 订单列表
     * @return 用户ID -> 用户信息的映射
     */
    private Map<String, VimUser> getUserInfoMap(List<OrderVO> orders) {
        // 收集所有需要查询的用户ID
        Set<Integer> userIds = new HashSet<>();
        for (OrderVO order : orders) {
            if (order.getAgent() != null) {
                try {
                    userIds.add(Integer.parseInt(order.getAgent()));
                } catch (NumberFormatException e) {
                    log.warn("代理ID格式错误: {}", order.getAgent());
                }
            }
            if (order.getAnchor() != null) {
                try {
                    userIds.add(Integer.parseInt(order.getAnchor()));
                } catch (NumberFormatException e) {
                    log.warn("主播ID格式错误: {}", order.getAnchor());
                }
            }
        }

        if (userIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询用户信息
        List<VimUser> users = vimUserMapper.selectByUserIds(new ArrayList<>(userIds));

        // 转换为Map，key为字符串类型的用户ID
        return users.stream()
                .collect(Collectors.toMap(
                        user -> user.getId().toString(),
                        user -> user,
                        (existing, replacement) -> existing // 如果有重复key，保留现有的
                ));
    }

    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, java.time.ZoneOffset.ofHours(8));
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // ==================== 私有方法 ====================

    /**
     * 验证创建请求参数
     */
    private void validateCreateRequest(OrderCreateRequest request) {
        if (!request.isValidTimeRange()) {
            throw new BusinessException("时间区间无效，结束时间必须大于开始时间");
        }

        if (request.getTimeRangeDays() > 365) {
            throw new BusinessException("时间区间不能超过365天");
        }

        if (request.getFeeRate().compareTo(BigDecimal.ZERO) <= 0 ||
                request.getFeeRate().compareTo(new BigDecimal("100")) > 0) {
            throw new BusinessException("劳务比例必须在0.01%到100%之间");
        }
    }

    /**
     * 检查是否已有重复订单
     */
    private boolean hasExistingOrder(String userId, Long startTime, Long endTime) {
        log.debug("检查重复订单: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        boolean exists = sysOrderMapper.existsOrderByUserAndTimeRange(userId, startTime, endTime);
        log.debug("重复订单检查结果: {}", exists);
        return exists;
    }

    /**
     * 创建代理订单（主订单 + 子订单）
     * 使用OperationsMapper统一获取充值数据，不再手动计算
     */
    private List<SysOrder> createAgentOrders(VimUser agent, OrderCreateRequest request) {
        List<SysOrder> orders = new ArrayList<>();

        // 1. 查询代理下的所有下级用户（包括普通用户、主播用户等）
        List<VimUser> subUsers = vimUserMapper.selectUsersByInviteUser(agent.getId());

        // 2. 创建主订单（代理本人）- 使用OperationsMapper获取统一数据
        SysOrder mainOrder = createOrder(agent, request, null, true);
        orders.add(mainOrder);
        log.info("为代理用户 {} 创建主订单，amount_fans将通过OperationsMapper统一设置", agent.getId());

        // 3. 为每个下级用户创建子订单（包括普通用户、主播用户等）
        for (VimUser subUser : subUsers) {
            // 根据用户身份确定是否为代理
            boolean isSubUserAgent = subUser.isAgent();
            SysOrder subOrder = createOrder(subUser, request, mainOrder.getPayid(), isSubUserAgent);
            orders.add(subOrder);
            log.info("为下级用户 {} 创建子订单，用户身份: {}", subUser.getId(),
                    subUser.getIdentity() == 1 ? "普通用户" :
                    subUser.getIdentity() == 2 ? "线上主播" :
                    subUser.getIdentity() == 3 ? "线下主播" :
                    subUser.getIdentity() == 4 ? "代理" : "未知");
        }

        return orders;
    }

    /**
     * 创建主播订单（独立订单）
     * 使用OperationsMapper统一获取充值数据，不再手动计算
     */
    private List<SysOrder> createAnchorOrders(VimUser anchor, OrderCreateRequest request) {
        List<SysOrder> orders = new ArrayList<>();

        // 创建独立订单 - 使用OperationsMapper获取统一数据
        // 对于主订单，amount_fans将在createOrder方法中通过OperationsMapper设置
        SysOrder order = createOrder(anchor, request, null, false);
        orders.add(order);
        log.info("为主播用户 {} 创建独立订单，amount_fans将通过OperationsMapper统一设置", anchor.getId());

        return orders;
    }
    /**
     * 计算单个用户的自充值金额（仅用于显示，不参与佣金计算）
     */
    private BigDecimal calculateUserSelfRechargeAmount(Integer userId, OrderCreateRequest request) {
        Integer startTime = request.getStartTime().intValue();
        Integer endTime = request.getEndTime().intValue();

        List<VimOrderRecharge> recharges = vimOrderRechargeMapper.selectSuccessRechargesByUser(userId, startTime, endTime);

        BigDecimal selfRechargeAmount = recharges.stream()
                .map(VimOrderRecharge::getSafeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.debug("用户 {} 自充值金额: {}", userId, selfRechargeAmount);
        return selfRechargeAmount;
    }

    /**
     * 统一的订单创建方法
     */
    private SysOrder createOrder(VimUser user, OrderCreateRequest request, String parentPayid,
                                 boolean isAgent) {
        SysOrder order = new SysOrder();

        // 基本信息
        String prefix = parentPayid == null ? (isAgent ? "MAIN" : "IND") : "SUB";
        order.setPayid(generateOrderId(prefix));
        order.setParentsPayid(parentPayid);

        if (isAgent) {
            order.setAgent(user.getId().toString());
            order.setAnchor(null);
        } else {
            order.setAgent(null);
            order.setAnchor(user.getId().toString());
        }

        // 设置金额信息
        // 1. 查询并设置用户的自充值金额（仅用于显示）
        BigDecimal selfRechargeAmount = calculateUserSelfRechargeAmount(user.getId(), request);
        order.setAmountSelf(selfRechargeAmount);

        // 2. amount_fans将在后面通过OperationsMapper设置
        order.setAmountFans(BigDecimal.ZERO);

        // 设置结算时间范围
        order.setSettlementStartTime(request.getStartTime());
        order.setSettlementEndTime(request.getEndTime());

        // 计算佣金（使用OperationsService获取统一的总充值金额）
        order.setFeeRate(request.getFeeRate());

        try {
            // 直接调用OperationsMapper获取统一的总充值金额
            int startTime = request.getStartTime().intValue();
            int endTime = request.getEndTime().intValue();

            // 确定rootUid（目标用户的vim_user ID）
            Integer rootUid = user.getId();

            // 根据用户身份确定查询层级：代理2层，主播1层
            Integer maxLevel = isAgent ? 2 : 1;

            SubordinateProfit operationsStats = operationsMapper.getSubordinateProfit(rootUid, maxLevel, startTime+"", endTime+ "");

            // 使用统一的总充值金额计算佣金：佣金 = 总充值 × 佣金比例
            BigDecimal totalRechargeAmount = operationsStats.getZongchongzhi() != null ? operationsStats.getZongchongzhi() : BigDecimal.ZERO;

            // 无论主订单还是子订单，都将OperationsMapper返回的总充值金额赋值给amount_fans
            order.setAmountFans(totalRechargeAmount);

            if (parentPayid == null) { // 主订单
                log.info("🎯 主订单 - 自充值: {}, 总充值(amount_fans): {}", selfRechargeAmount, totalRechargeAmount);
            } else { // 子订单
                log.info("🎯 子订单 - 自充值: {}, 总充值(amount_fans): {}", selfRechargeAmount, totalRechargeAmount);
            }

            BigDecimal commissionAmount = calculateCommission(totalRechargeAmount, request.getFeeRate());
            order.setFeeValue(commissionAmount);
            order.setFeeActual(commissionAmount);

            log.info("✅ 订单创建完成 - 用户: {}, 身份: {}, 层级: {}, 自充值: {}, 总充值: {}, 佣金比例: {}%, 佣金: {}",
                    user.getId(), isAgent ? "代理" : "主播", maxLevel, selfRechargeAmount, totalRechargeAmount, request.getFeeRate(), commissionAmount);

        } catch (Exception e) {
            log.warn("❌ 订单创建时调用OperationsMapper失败，使用备用方案: {}", e.getMessage());

            // 备用方案：amount_fans设置为自充值金额（不够准确，但至少有数据）
            order.setAmountFans(selfRechargeAmount);

            // 佣金计算基于自充值金额
            BigDecimal commissionAmount = calculateCommission(selfRechargeAmount, request.getFeeRate());
            order.setFeeValue(commissionAmount);
            order.setFeeActual(commissionAmount);

            log.info("🔄 订单创建备用方案 - 用户: {}, 自充值: {}, 佣金: {}",
                    user.getId(), selfRechargeAmount, commissionAmount);
        }

        // 收款信息
        order.setFeePerson(request.getFeePerson());
        order.setFeeAccount(request.getFeeAccount());

        // 状态信息
        order.setOrderStatusEnum(OrderStatus.CALCULATED);
        order.setSettlementStatusEnum(SettlementStatus.PENDING);
        order.setVimUserId(user.getId());

        return order;
    }

    /**
     * 计算佣金
     */
    private BigDecimal calculateCommission(BigDecimal amount, BigDecimal feeRate) {
        if (amount == null || feeRate == null) {
            return BigDecimal.ZERO;
        }
        return amount.multiply(feeRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 生成订单ID
     */
    private String generateOrderId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" +
                String.format("%04d", new Random().nextInt(10000));
    }

    /**
     * 检查订单是否属于当前用户
     */
    private boolean isUserOrder(SysOrder order, String currentUserId) {
        if (order == null || !StringUtils.hasText(currentUserId)) {
            return true;
        }
        return !currentUserId.equals(order.getAgent()) && !currentUserId.equals(order.getAnchor());
    }

    /**
     * 批量验证订单归属权限
     */
    private void validateOrdersOwnership(List<String> payids, String currentUserId) {
        // 管理员可以操作所有订单
        if (SecurityUtils.isSuperAdmin() || SecurityUtils.isAdmin()) {
            return;
        }
        for (String payid : payids) {
            SysOrder order = sysOrderMapper.selectOneById(payid);
            if (order == null) {
                throw new BusinessException("订单不存在: " + payid);
            }
            if (isUserOrder(order, currentUserId)) {
                throw new BusinessException("无权限操作订单: " + payid);
            }
        }
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper buildQueryWrapper(OrderQueryRequest request, String currentUserId) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        // 权限过滤：管理员可以查看所有订单，普通用户只能查看自己相关的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            queryWrapper.and("(agent = '" + currentUserId + "' OR anchor = '" + currentUserId + "')");
        }

        // 订单ID
        if (StringUtils.hasText(request.getPayid())) {
            queryWrapper.eq("payid", request.getPayid());
        }

        // 父订单ID
        if (StringUtils.hasText(request.getParentsPayid())) {
            queryWrapper.eq("parents_payid", request.getParentsPayid());
        }

        // 代理ID
        if (StringUtils.hasText(request.getAgent())) {
            queryWrapper.eq("agent", request.getAgent());
        }

        // 主播ID
        if (StringUtils.hasText(request.getAnchor())) {
            queryWrapper.eq("anchor", request.getAnchor());
        }

        // 订单状态
        if (request.getOrderStatus() != null) {
            queryWrapper.eq("order_status", request.getOrderStatus());
        }

        // 结算状态
        if (request.getSettlementStatus() != null) {
            queryWrapper.eq("settlement_status", request.getSettlementStatus());
        }

        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge("create_time", request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le("create_time", request.getCreateTimeEnd());
        }

        // 更新时间范围
        if (request.getUpdateTimeStart() != null) {
            queryWrapper.ge("update_time", request.getUpdateTimeStart());
        }
        if (request.getUpdateTimeEnd() != null) {
            queryWrapper.le("update_time", request.getUpdateTimeEnd());
        }

        // 主订单/子订单过滤
        if (Boolean.TRUE.equals(request.getMainOrderOnly())) {
            queryWrapper.isNull("parents_payid");
        }
        if (Boolean.TRUE.equals(request.getSubOrderOnly())) {
            queryWrapper.isNotNull("parents_payid");
        }

        // 选中的订单ID过滤（用于导出）
        if (request.getSelectedOrderIds() != null && !request.getSelectedOrderIds().isEmpty()) {
            queryWrapper.in("payid", request.getSelectedOrderIds());
        }

        // 排序
        queryWrapper.orderBy("create_time", false);

        return queryWrapper;
    }


    /**
     * 批量转换为OrderVO（优化用户昵称查询）
     */
    private List<OrderVO> batchConvertToOrderVOs(List<SysOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }

        // 收集所有用户ID
        Set<Integer> userIds = new HashSet<>();
        for (SysOrder order : orders) {
            if (StringUtils.hasText(order.getAgent())) {
                try {
                    userIds.add(Integer.valueOf(order.getAgent()));
                } catch (NumberFormatException e) {
                    log.warn("无效的代理ID: {}", order.getAgent());
                }
            }
            if (StringUtils.hasText(order.getAnchor())) {
                try {
                    userIds.add(Integer.valueOf(order.getAnchor()));
                } catch (NumberFormatException e) {
                    log.warn("无效的主播ID: {}", order.getAnchor());
                }
            }
        }

        // 批量查询用户信息
        Map<Integer, VimUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<VimUser> users = vimUserMapper.selectByUserIds(new ArrayList<>(userIds));
            userMap = users.stream().collect(Collectors.toMap(VimUser::getId, user -> user));
        }

        // 转换为VO
        List<OrderVO> result = new ArrayList<>();
        for (SysOrder order : orders) {
            OrderVO vo = convertToOrderVO(order);

            // 设置用户昵称
            if (StringUtils.hasText(order.getAgent())) {
                try {
                    VimUser agent = userMap.get(Integer.valueOf(order.getAgent()));
                    if (agent != null) {
                        vo.setAgentNickname(agent.getNickname());
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的代理ID: {}", order.getAgent());
                }
            }

            if (StringUtils.hasText(order.getAnchor())) {
                try {
                    VimUser anchor = userMap.get(Integer.valueOf(order.getAnchor()));
                    if (anchor != null) {
                        vo.setAnchorNickname(anchor.getNickname());
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的主播ID: {}", order.getAnchor());
                }
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 转换为OrderVO
     */
    private OrderVO convertToOrderVO(SysOrder order) {
        OrderVO vo = new OrderVO();

        // 基本信息
        vo.setPayid(order.getPayid());
        vo.setParentsPayid(order.getParentsPayid());
        vo.setAgent(order.getAgent());
        vo.setAnchor(order.getAnchor());

        // 金额信息
        vo.setAmountSelf(order.getAmountSelf());
        vo.setAmountFans(order.getAmountFans());
        vo.setTotalAmount(order.getAmountFans());
        vo.setFeeRate(order.getFeeRate());
        vo.setFeeValue(order.getFeeValue());
        vo.setFeeActual(order.getFeeActual());

        // 收款信息
        vo.setFeePerson(order.getFeePerson());
        vo.setFeeAccount(order.getFeeAccount());

        // 状态信息
        vo.setOrderStatusEnum(order.getOrderStatusEnum());
        vo.setSettlementStatusEnum(order.getSettlementStatusEnum());

        // 时间信息
        vo.setCreateTime(order.getCreateTime());
        vo.setUpdateTime(order.getUpdateTime());
        vo.setSettlementStartTime(order.getSettlementStartTime());
        vo.setSettlementEndTime(order.getSettlementEndTime());

        // 业务信息
        vo.setIsMainOrder(order.isMainOrder());

        return vo;
    }
}
