package com.jcloud.admin.service.order;

import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.vo.OrderVO;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单查询服务
 * 负责订单查询相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderQueryService {
    
    private final SysOrderMapper sysOrderMapper;
    private final VimUserMapper vimUserMapper;
    
    /**
     * 分页查询订单
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 分页结果
     */
    public PageResult<OrderVO> pageOrders(OrderQueryRequest request, String currentUserId) {
        log.info("分页查询订单列表，用户ID: {}, 请求: {}", currentUserId, request);
        
        // 构建查询条件（自动过滤当前用户的数据）
        QueryWrapper queryWrapper = buildQueryWrapper(request, currentUserId);
        
        // 只查询主订单（没有父订单的订单）
        queryWrapper.isNull("parents_payid");
        
        // 分页查询主订单
        Page<SysOrder> page = Page.of(request.getPageNum(), request.getPageSize());
        Page<SysOrder> result = sysOrderMapper.paginate(page, queryWrapper);
        
        // 批量转换为VO（优化用户昵称查询）
        List<OrderVO> orderVOs = batchConvertToOrderVOs(result.getRecords());
        
        return PageResult.of(orderVOs, result.getTotalRow(), request.getPageNum(), request.getPageSize());
    }
    
    /**
     * 获取订单详情
     * 
     * @param payid 订单ID
     * @param currentUserId 当前用户ID
     * @return 订单详情
     */
    public OrderVO getOrderDetail(String payid, String currentUserId) {
        log.info("查询订单详情，订单ID: {}, 用户ID: {}", payid, currentUserId);
        
        // 查询订单
        SysOrder order = sysOrderMapper.selectOneById(payid);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 权限验证
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            if (!isUserOrder(order, currentUserId)) {
                throw new BusinessException("无权查看此订单");
            }
        }
        
        return convertToOrderVO(order);
    }
    
    /**
     * 获取主订单及其子订单
     * 
     * @param parentsPayid 主订单ID
     * @param currentUserId 当前用户ID
     * @return 主订单（包含子订单信息）
     */
    public OrderVO getMainOrderWithSubOrders(String parentsPayid, String currentUserId) {
        // 查询主订单
        OrderVO mainOrder = getOrderDetail(parentsPayid, currentUserId);
        
        // 查询子订单
        List<OrderVO> subOrders = getSubOrdersByMainOrderId(parentsPayid, currentUserId);
        
        // 设置子订单信息
        mainOrder.setSubOrders(subOrders);
        mainOrder.setSubOrderCount(subOrders.size());
        
        return mainOrder;
    }
    
    /**
     * 获取子订单列表
     * 
     * @param mainOrderId 主订单ID
     * @param currentUserId 当前用户ID
     * @return 子订单列表
     */
    public List<OrderVO> getSubOrdersByMainOrderId(String mainOrderId, String currentUserId) {
        log.info("查询子订单列表，主订单ID: {}, 用户ID: {}", mainOrderId, currentUserId);
        
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
            .eq("parents_payid", mainOrderId)
            .orderBy("create_time", true);
        
        // 权限过滤
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            queryWrapper.and(wrapper -> wrapper
                .eq("user_id", currentUserId)
                .or()
                .eq("agent", currentUserId)
            );
        }
        
        List<SysOrder> subOrders = sysOrderMapper.selectListByQuery(queryWrapper);
        return batchConvertToOrderVOs(subOrders);
    }
    
    /**
     * 导出订单数据
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 订单列表
     */
    public List<OrderVO> exportOrders(OrderQueryRequest request, String currentUserId) {
        log.info("导出订单数据，用户ID: {}, 请求: {}", currentUserId, request);
        
        // 构建查询条件
        QueryWrapper queryWrapper = buildQueryWrapper(request, currentUserId);
        
        // 查询所有匹配的订单（包括主订单和子订单）
        List<SysOrder> orders = sysOrderMapper.selectListByQuery(queryWrapper);
        
        return batchConvertToOrderVOs(orders);
    }
    
    /**
     * 构建查询条件
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return 查询包装器
     */
    private QueryWrapper buildQueryWrapper(OrderQueryRequest request, String currentUserId) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        // 权限过滤：管理员可以查看所有订单，普通用户只能查看自己相关的订单
        if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.isAdmin()) {
            queryWrapper.and(wrapper -> wrapper
                .eq("user_id", currentUserId)
                .or()
                .eq("agent", currentUserId)
            );
        }
        
        // 时间范围过滤
        if (request.getStartTime() != null) {
            queryWrapper.ge("start_time", request.getStartTime());
        }
        if (request.getEndTime() != null) {
            queryWrapper.le("end_time", request.getEndTime());
        }
        
        // 订单状态过滤
        if (request.getOrderStatus() != null) {
            queryWrapper.eq("order_status", request.getOrderStatus());
        }
        
        // 结算状态过滤
        if (request.getSettlementStatus() != null) {
            queryWrapper.eq("settlement_status", request.getSettlementStatus());
        }
        
        // 用户ID过滤
        if (StringUtils.hasText(request.getUserId())) {
            queryWrapper.eq("user_id", request.getUserId());
        }
        
        // 代理ID过滤
        if (StringUtils.hasText(request.getAgent())) {
            queryWrapper.eq("agent", request.getAgent());
        }
        
        // 主播ID过滤
        if (StringUtils.hasText(request.getAnchor())) {
            queryWrapper.eq("anchor", request.getAnchor());
        }
        
        // 订单ID模糊查询
        if (StringUtils.hasText(request.getPayid())) {
            queryWrapper.like("payid", request.getPayid());
        }
        
        // 默认按创建时间倒序排列
        queryWrapper.orderBy("create_time", false);
        
        return queryWrapper;
    }
    
    /**
     * 批量转换为OrderVO
     * 
     * @param orders 订单列表
     * @return OrderVO列表
     */
    private List<OrderVO> batchConvertToOrderVOs(List<SysOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }
        
        // 收集所有需要查询的用户ID
        Set<Integer> userIds = new HashSet<>();
        for (SysOrder order : orders) {
            if (StringUtils.hasText(order.getUserId())) {
                try {
                    userIds.add(Integer.valueOf(order.getUserId()));
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID: {}", order.getUserId());
                }
            }
            if (StringUtils.hasText(order.getAgent())) {
                try {
                    userIds.add(Integer.valueOf(order.getAgent()));
                } catch (NumberFormatException e) {
                    log.warn("无效的代理ID: {}", order.getAgent());
                }
            }
            if (StringUtils.hasText(order.getAnchor())) {
                try {
                    userIds.add(Integer.valueOf(order.getAnchor()));
                } catch (NumberFormatException e) {
                    log.warn("无效的主播ID: {}", order.getAnchor());
                }
            }
        }
        
        // 批量查询用户信息
        Map<String, VimUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<VimUser> users = vimUserMapper.selectBatchIds(userIds);
            userMap = users.stream()
                .collect(Collectors.toMap(
                    user -> user.getId().toString(),
                    user -> user,
                    (existing, replacement) -> existing
                ));
        }
        
        // 转换为VO
        List<OrderVO> result = new ArrayList<>();
        for (SysOrder order : orders) {
            OrderVO vo = convertToOrderVO(order);
            
            // 设置用户昵称
            if (StringUtils.hasText(order.getUserId())) {
                VimUser user = userMap.get(order.getUserId());
                if (user != null) {
                    vo.setUserNickname(user.getNickname());
                }
            }
            
            // 设置代理昵称
            if (StringUtils.hasText(order.getAgent())) {
                VimUser agent = userMap.get(order.getAgent());
                if (agent != null) {
                    vo.setAgentNickname(agent.getNickname());
                }
            }
            
            // 设置主播昵称
            if (StringUtils.hasText(order.getAnchor())) {
                VimUser anchor = userMap.get(order.getAnchor());
                if (anchor != null) {
                    vo.setAnchorNickname(anchor.getNickname());
                }
            }
            
            result.add(vo);
        }
        
        return result;
    }
    
    /**
     * 转换为OrderVO
     * 
     * @param order 订单实体
     * @return OrderVO
     */
    private OrderVO convertToOrderVO(SysOrder order) {
        OrderVO vo = new OrderVO();
        
        // 基本信息
        vo.setPayid(order.getPayid());
        vo.setParentsPayid(order.getParentsPayid());
        vo.setUserId(order.getUserId());
        vo.setAgent(order.getAgent());
        vo.setAnchor(order.getAnchor());
        
        // 时间信息
        vo.setStartTime(order.getStartTime());
        vo.setEndTime(order.getEndTime());
        vo.setCreateTime(order.getCreateTime());
        vo.setSettlementTime(order.getSettlementTime());
        
        // 金额信息
        vo.setTotalAmount(order.getTotalAmount());
        vo.setCommissionAmount(order.getCommissionAmount());
        vo.setActualAmount(order.getActualAmount());
        
        // 状态信息
        vo.setOrderStatus(order.getOrderStatus());
        vo.setSettlementStatus(order.getSettlementStatus());
        
        // 格式化时间显示
        vo.setCreateTimeDisplay(formatTimestamp(order.getCreateTime()));
        vo.setSettlementTimeDisplay(formatTimestamp(order.getSettlementTime()));
        
        return vo;
    }
    
    /**
     * 格式化时间戳
     * 
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, 
            java.time.ZoneOffset.ofHours(8));
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 检查订单是否属于指定用户
     * 
     * @param order 订单
     * @param currentUserId 当前用户ID
     * @return 是否属于用户
     */
    private boolean isUserOrder(SysOrder order, String currentUserId) {
        if (order == null || !StringUtils.hasText(currentUserId)) {
            return true;
        }
        
        // 检查订单的用户ID或代理ID是否匹配
        return currentUserId.equals(order.getUserId()) || 
               currentUserId.equals(order.getAgent());
    }
}
