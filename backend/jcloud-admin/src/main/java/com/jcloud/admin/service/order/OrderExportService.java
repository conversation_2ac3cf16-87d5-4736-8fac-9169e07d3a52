package com.jcloud.admin.service.order;

import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.vo.OrderVO;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单导出服务
 * 负责订单Excel导出相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderExportService {
    
    private final OrderQueryService orderQueryService;
    private final VimUserMapper vimUserMapper;
    
    /**
     * 导出订单到Excel
     * 
     * @param request 查询请求
     * @param currentUserId 当前用户ID
     * @return Excel文件字节数组
     */
    public byte[] exportOrdersToExcel(OrderQueryRequest request, String currentUserId) {
        // 获取订单数据
        List<OrderVO> orders = orderQueryService.exportOrders(request, currentUserId);
        
        // 获取所有相关用户信息（代理和主播）
        Map<String, VimUser> userInfoMap = getUserInfoMap(orders);
        
        try {
            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("代理结算");
            
            // 重新设计分组逻辑：按主订单分组，每组包含主订单和其子订单
            Map<String, OrderGroup> orderGroups = buildOrderGroups(orders, userInfoMap);
            
            int currentRow = 0;
            
            log.info("开始生成Excel文件，共有 {} 个订单组", orderGroups.size());
            
            // 创建增强的Excel样式
            ExcelStyles styles = createExcelStyles(workbook);
            
            // 设置列宽
            setColumnWidths(sheet);
            
            // 生成每个订单组的数据
            for (OrderGroup orderGroup : orderGroups.values()) {
                currentRow = generateOrderGroupRows(sheet, orderGroup, currentRow, styles, userInfoMap);
                currentRow++; // 组间空行
            }
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();
            
            log.info("Excel文件生成完成，总行数: {}", currentRow);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("生成Excel文件失败", e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }
    
    /**
     * Excel样式类
     */
    @Getter
    private static class ExcelStyles {
        public CellStyle headerStyle;        // 分组标题样式（代理信息）
        public CellStyle titleStyle;         // 表头样式（订单信息）
        public CellStyle dataStyle;          // 数据行样式（订单明细）
        public CellStyle summaryStyle;       // 合计行样式
        public CellStyle amountStyle;        // 金额样式
    }
    
    /**
     * 创建Excel样式
     */
    private ExcelStyles createExcelStyles(Workbook workbook) {
        ExcelStyles styles = new ExcelStyles();
        
        // 1. 分组标题样式（代理信息表头：浅绿色背景 + 黑色文字）
        styles.headerStyle = workbook.createCellStyle();
        styles.headerStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        styles.headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.headerStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        styles.headerStyle.setFont(headerFont);
        setBorders(styles.headerStyle);
        
        // 2. 表头样式（订单信息表头：浅蓝色背景 + 白色文字）
        styles.titleStyle = workbook.createCellStyle();
        styles.titleStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        styles.titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.titleStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 10);
        titleFont.setColor(IndexedColors.WHITE.getIndex());
        styles.titleStyle.setFont(titleFont);
        setBorders(styles.titleStyle);
        
        // 3. 数据行样式（订单明细：白色背景 + 黑色文字）
        styles.dataStyle = workbook.createCellStyle();
        styles.dataStyle.setAlignment(HorizontalAlignment.LEFT);
        styles.dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 9);
        dataFont.setColor(IndexedColors.BLACK.getIndex());
        styles.dataStyle.setFont(dataFont);
        setBorders(styles.dataStyle);
        
        // 4. 合计行样式（浅黄色背景 + 黑色粗体文字）
        styles.summaryStyle = workbook.createCellStyle();
        styles.summaryStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        styles.summaryStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.summaryStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.summaryStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font summaryFont = workbook.createFont();
        summaryFont.setBold(true);
        summaryFont.setFontHeightInPoints((short) 10);
        summaryFont.setColor(IndexedColors.BLACK.getIndex());
        styles.summaryStyle.setFont(summaryFont);
        setBorders(styles.summaryStyle);
        
        // 5. 金额样式（数字格式）
        styles.amountStyle = workbook.createCellStyle();
        styles.amountStyle.cloneStyleFrom(styles.dataStyle);
        styles.amountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        styles.amountStyle.setAlignment(HorizontalAlignment.RIGHT);
        
        return styles;
    }
    
    /**
     * 设置边框
     */
    private void setBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }
    
    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 3000);   // 订单类型
        sheet.setColumnWidth(1, 5000);   // 结算时间
        sheet.setColumnWidth(2, 6000);   // 编号
        sheet.setColumnWidth(3, 4000);   // 昵称
        sheet.setColumnWidth(4, 4000);   // 总充值
        sheet.setColumnWidth(5, 4000);   // 佣金金额
        sheet.setColumnWidth(6, 4000);   // 实际金额
        sheet.setColumnWidth(7, 3000);   // 状态
    }
    
    /**
     * 订单分组类
     */
    @Getter
    private static class OrderGroup {
        private final OrderVO mainOrder;
        private final List<OrderVO> subOrders;
        private final String groupKey;
        
        public OrderGroup(OrderVO mainOrder, String groupKey) {
            this.mainOrder = mainOrder;
            this.subOrders = new ArrayList<>();
            this.groupKey = groupKey;
        }
        
        public void addSubOrder(OrderVO subOrder) {
            this.subOrders.add(subOrder);
        }
        
        public List<OrderVO> getAllOrders() {
            List<OrderVO> allOrders = new ArrayList<>();
            allOrders.add(mainOrder);
            allOrders.addAll(subOrders);
            return allOrders;
        }
        
        public BigDecimal getTotalAmount() {
            return getAllOrders().stream()
                .map(OrderVO::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        
        public BigDecimal getTotalCommission() {
            return getAllOrders().stream()
                .map(OrderVO::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        
        public BigDecimal getTotalActualAmount() {
            return getAllOrders().stream()
                .map(OrderVO::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }
    
    /**
     * 构建订单分组
     */
    private Map<String, OrderGroup> buildOrderGroups(List<OrderVO> orders, Map<String, VimUser> userInfoMap) {
        Map<String, OrderGroup> orderGroups = new LinkedHashMap<>();
        Map<String, OrderVO> mainOrderMap = new HashMap<>();
        
        // 第一遍：识别主订单和子订单
        for (OrderVO order : orders) {
            if (order.getParentsPayid() == null) {
                // 主订单
                String groupKey = order.getAgent() != null ? order.getAgent() : order.getUserId();
                OrderGroup group = new OrderGroup(order, groupKey);
                orderGroups.put(order.getPayid(), group);
                mainOrderMap.put(order.getPayid(), order);
            }
        }
        
        // 第二遍：将子订单分配到对应的主订单组
        for (OrderVO order : orders) {
            if (order.getParentsPayid() != null) {
                // 子订单
                OrderGroup group = orderGroups.get(order.getParentsPayid());
                if (group != null) {
                    group.addSubOrder(order);
                } else {
                    // 如果找不到对应的主订单组，创建一个新的组
                    log.warn("找不到子订单 {} 对应的主订单 {}", order.getPayid(), order.getParentsPayid());
                    String groupKey = order.getAgent() != null ? order.getAgent() : order.getUserId();
                    OrderGroup newGroup = new OrderGroup(order, groupKey);
                    orderGroups.put(order.getPayid(), newGroup);
                }
            }
        }
        
        return orderGroups;
    }
    
    /**
     * 生成订单组的行数据
     */
    private int generateOrderGroupRows(Sheet sheet, OrderGroup orderGroup, int startRow, 
                                     ExcelStyles styles, Map<String, VimUser> userInfoMap) {
        int currentRow = startRow;
        
        // 1. 代理信息标题行
        Row agentHeaderRow = sheet.createRow(currentRow++);
        String agentInfo = getAgentInfo(orderGroup.getMainOrder(), userInfoMap);
        Cell agentCell = agentHeaderRow.createCell(0);
        agentCell.setCellValue(agentInfo);
        agentCell.setCellStyle(styles.headerStyle);
        
        // 合并代理信息行的所有列
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(
            currentRow - 1, currentRow - 1, 0, 7));
        
        // 2. 表头行
        Row headerRow = sheet.createRow(currentRow++);
        String[] headers = {"订单类型", "结算时间", "编号", "昵称", "总充值", "佣金金额", "实际金额", "状态"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.titleStyle);
        }
        
        // 3. 订单数据行
        for (OrderVO order : orderGroup.getAllOrders()) {
            currentRow = addOrderDataRow(sheet, order, currentRow, styles, userInfoMap);
        }
        
        // 4. 合计行
        currentRow = addSummaryRow(sheet, orderGroup, currentRow, styles);
        
        return currentRow;
    }
    
    /**
     * 添加订单数据行
     */
    private int addOrderDataRow(Sheet sheet, OrderVO order, int rowIndex, 
                               ExcelStyles styles, Map<String, VimUser> userInfoMap) {
        Row row = sheet.createRow(rowIndex);
        int colIndex = 0;
        
        // 订单类型
        String orderType = order.getParentsPayid() == null ? "主订单" : "子订单";
        Cell typeCell = row.createCell(colIndex++);
        typeCell.setCellValue(orderType);
        typeCell.setCellStyle(styles.dataStyle);
        
        // 结算时间
        String settlementTime = getSettlementTimeDisplay(order);
        Cell timeCell = row.createCell(colIndex++);
        timeCell.setCellValue(settlementTime);
        timeCell.setCellStyle(styles.dataStyle);
        
        // 编号
        Cell idCell = row.createCell(colIndex++);
        idCell.setCellValue(order.getPayid());
        idCell.setCellStyle(styles.dataStyle);
        
        // 昵称
        String nickname = getUserNickname(order, userInfoMap);
        Cell nicknameCell = row.createCell(colIndex++);
        nicknameCell.setCellValue(nickname);
        nicknameCell.setCellStyle(styles.dataStyle);
        
        // 总充值
        Cell totalAmountCell = row.createCell(colIndex++);
        if (order.getTotalAmount() != null) {
            totalAmountCell.setCellValue(order.getTotalAmount().doubleValue());
        } else {
            totalAmountCell.setCellValue(0.0);
        }
        totalAmountCell.setCellStyle(styles.amountStyle);
        
        // 佣金金额
        Cell commissionCell = row.createCell(colIndex++);
        if (order.getCommissionAmount() != null) {
            commissionCell.setCellValue(order.getCommissionAmount().doubleValue());
        } else {
            commissionCell.setCellValue(0.0);
        }
        commissionCell.setCellStyle(styles.amountStyle);
        
        // 实际金额
        Cell actualAmountCell = row.createCell(colIndex++);
        if (order.getActualAmount() != null) {
            actualAmountCell.setCellValue(order.getActualAmount().doubleValue());
        } else {
            actualAmountCell.setCellValue(0.0);
        }
        actualAmountCell.setCellStyle(styles.amountStyle);
        
        // 状态
        String status = getOrderStatusDisplay(order);
        Cell statusCell = row.createCell(colIndex);
        statusCell.setCellValue(status);
        statusCell.setCellStyle(styles.dataStyle);
        
        return rowIndex + 1;
    }
    
    /**
     * 添加合计行
     */
    private int addSummaryRow(Sheet sheet, OrderGroup orderGroup, int rowIndex, ExcelStyles styles) {
        Row summaryRow = sheet.createRow(rowIndex);
        int colIndex = 0;
        
        // 合计标签
        Cell summaryLabelCell = summaryRow.createCell(colIndex++);
        summaryLabelCell.setCellValue("合计");
        summaryLabelCell.setCellStyle(styles.summaryStyle);
        
        // 空列
        for (int i = 1; i < 4; i++) {
            Cell emptyCell = summaryRow.createCell(colIndex++);
            emptyCell.setCellValue("");
            emptyCell.setCellStyle(styles.summaryStyle);
        }
        
        // 总充值合计
        Cell totalAmountSumCell = summaryRow.createCell(colIndex++);
        totalAmountSumCell.setCellValue(orderGroup.getTotalAmount().doubleValue());
        CellStyle totalAmountSumStyle = createSubOrderAmountStyle(styles.summaryStyle, sheet.getWorkbook());
        totalAmountSumCell.setCellStyle(totalAmountSumStyle);
        
        // 佣金合计
        Cell commissionSumCell = summaryRow.createCell(colIndex++);
        commissionSumCell.setCellValue(orderGroup.getTotalCommission().doubleValue());
        CellStyle commissionSumStyle = createSubOrderAmountStyle(styles.summaryStyle, sheet.getWorkbook());
        commissionSumCell.setCellStyle(commissionSumStyle);
        
        // 实际金额合计
        Cell actualAmountSumCell = summaryRow.createCell(colIndex++);
        actualAmountSumCell.setCellValue(orderGroup.getTotalActualAmount().doubleValue());
        CellStyle actualAmountSumStyle = createSubOrderAmountStyle(styles.summaryStyle, sheet.getWorkbook());
        actualAmountSumCell.setCellStyle(actualAmountSumStyle);
        
        // 状态列
        Cell statusSumCell = summaryRow.createCell(colIndex);
        statusSumCell.setCellValue("");
        statusSumCell.setCellStyle(styles.summaryStyle);
        
        return rowIndex + 1;
    }
    
    /**
     * 创建金额样式
     */
    private CellStyle createSubOrderAmountStyle(CellStyle baseStyle, Workbook workbook) {
        CellStyle amountStyle = workbook.createCellStyle();
        amountStyle.cloneStyleFrom(baseStyle);
        amountStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        amountStyle.setAlignment(HorizontalAlignment.RIGHT);
        return amountStyle;
    }
    
    /**
     * 获取代理信息
     */
    private String getAgentInfo(OrderVO order, Map<String, VimUser> userInfoMap) {
        if (order.getAgent() != null) {
            VimUser agent = userInfoMap.get(order.getAgent());
            if (agent != null) {
                return String.format("代理：%s (ID: %s)", agent.getNickname(), order.getAgent());
            }
        }
        return String.format("用户：%s", order.getUserId());
    }
    
    /**
     * 获取结算时间显示
     */
    private String getSettlementTimeDisplay(OrderVO order) {
        if (order.getOrderStatus() == null) {
            return "状态未知";
        }
        
        switch (order.getOrderStatus()) {
            case 0:
                return "待结算";
            case 1:
                if (order.getSettlementTime() != null) {
                    return formatTimestamp(order.getSettlementTime());
                } else {
                    return "已结算";
                }
            case 2:
                return "已取消";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 获取用户昵称
     */
    private String getUserNickname(OrderVO order, Map<String, VimUser> userInfoMap) {
        if (order.getAnchor() != null) {
            VimUser anchorUser = userInfoMap.get(order.getAnchor());
            if (anchorUser != null) {
                return anchorUser.getNickname();
            }
        }
        
        if (order.getUserId() != null) {
            VimUser user = userInfoMap.get(order.getUserId());
            if (user != null) {
                return user.getNickname();
            }
        }
        
        return "未知用户";
    }
    
    /**
     * 获取订单状态显示
     */
    private String getOrderStatusDisplay(OrderVO order) {
        if (order.getOrderStatus() == null) {
            return "未知";
        }
        
        switch (order.getOrderStatus()) {
            case 0:
                return "待结算";
            case 1:
                return "已结算";
            case 2:
                return "已取消";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取用户信息映射
     */
    private Map<String, VimUser> getUserInfoMap(List<OrderVO> orders) {
        // 收集所有需要查询的用户ID
        Set<Integer> userIds = new HashSet<>();
        for (OrderVO order : orders) {
            if (order.getUserId() != null) {
                try {
                    userIds.add(Integer.valueOf(order.getUserId()));
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID: {}", order.getUserId());
                }
            }
            if (order.getAgent() != null) {
                try {
                    userIds.add(Integer.valueOf(order.getAgent()));
                } catch (NumberFormatException e) {
                    log.warn("无效的代理ID: {}", order.getAgent());
                }
            }
            if (order.getAnchor() != null) {
                try {
                    userIds.add(Integer.valueOf(order.getAnchor()));
                } catch (NumberFormatException e) {
                    log.warn("无效的主播ID: {}", order.getAnchor());
                }
            }
        }
        
        // 批量查询用户信息
        if (userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<VimUser> users = vimUserMapper.selectBatchIds(userIds);
        return users.stream()
            .collect(Collectors.toMap(
                user -> user.getId().toString(),
                user -> user,
                (existing, replacement) -> existing
            ));
    }
    
    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, 
            java.time.ZoneOffset.ofHours(8));
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
