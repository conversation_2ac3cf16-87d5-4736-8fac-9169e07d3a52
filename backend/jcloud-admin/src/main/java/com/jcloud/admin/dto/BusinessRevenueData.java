package com.jcloud.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业务收入数据DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "业务收入数据")
public class BusinessRevenueData {
    
    /**
     * 日期
     */
    @Schema(description = "日期", example = "2025-01-20")
    private String date;
    
    /**
     * 业务类型
     * 1：开箱 2：兑换 3：对战 5：升级 6：补偿 7：Roll房 8：奖励 9：融合
     */
    @Schema(description = "业务类型", example = "1")
    private Integer businessType;
    
    /**
     * 订单数量
     */
    @Schema(description = "订单数量", example = "150")
    private Integer orderCount;
    
    /**
     * 总金额
     */
    @Schema(description = "总金额", example = "5000.00")
    private BigDecimal totalAmount;
    
    /**
     * 平均金额
     */
    @Schema(description = "平均金额", example = "33.33")
    private BigDecimal avgAmount;
    
    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        if (businessType == null) return "未知";
        
        switch (businessType) {
            case 1: return "开箱";
            case 2: return "兑换";
            case 3: return "对战";
            case 5: return "升级";
            case 6: return "补偿";
            case 7: return "Roll房";
            case 8: return "奖励";
            case 9: return "融合";
            default: return "其他";
        }
    }
}
