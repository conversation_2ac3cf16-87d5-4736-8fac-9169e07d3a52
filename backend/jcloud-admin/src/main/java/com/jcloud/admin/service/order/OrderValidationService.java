package com.jcloud.admin.service.order;

import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOrderMapper;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 订单验证服务
 * 负责订单相关的验证逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderValidationService {
    
    private final SysOrderMapper sysOrderMapper;
    
    /**
     * 验证订单创建请求
     * 
     * @param request 创建请求
     */
    public void validateCreateRequest(OrderCreateRequest request) {
        if (!request.isValidTimeRange()) {
            throw new BusinessException("时间区间无效，结束时间必须大于开始时间");
        }
        
        if (request.getTargetUserId() == null) {
            throw new BusinessException("目标用户ID不能为空");
        }
        
        // 验证时间范围合理性（不能超过一年）
        long timeRangeSeconds = request.getEndTime() - request.getStartTime();
        long maxTimeRangeSeconds = 365L * 24 * 60 * 60; // 一年
        if (timeRangeSeconds > maxTimeRangeSeconds) {
            throw new BusinessException("时间范围不能超过一年");
        }
        
        log.debug("订单创建请求验证通过: {}", request);
    }
    
    /**
     * 检查是否存在重复订单
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否存在重复订单
     */
    public boolean hasExistingOrder(String userId, Long startTime, Long endTime) {
        log.debug("检查重复订单: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        boolean exists = sysOrderMapper.existsOrderByUserAndTimeRange(userId, startTime, endTime);
        log.debug("重复订单检查结果: {}", exists);
        return exists;
    }
    
    /**
     * 验证订单所有权
     * 
     * @param payids 订单ID列表
     * @param currentUserId 当前用户ID
     */
    public void validateOrdersOwnership(List<String> payids, String currentUserId) {
        // 管理员可以操作所有订单
        if (SecurityUtils.isSuperAdmin() || SecurityUtils.isAdmin()) {
            return;
        }
        
        // 验证每个订单是否属于当前用户
        for (String payid : payids) {
            SysOrder order = sysOrderMapper.selectOneById(payid);
            if (order == null) {
                throw new BusinessException("订单不存在: " + payid);
            }
            
            if (isUserOrder(order, currentUserId)) {
                throw new BusinessException("无权操作订单: " + payid);
            }
        }
    }
    
    /**
     * 检查订单是否属于指定用户
     * 
     * @param order 订单
     * @param currentUserId 当前用户ID
     * @return 是否属于用户
     */
    public boolean isUserOrder(SysOrder order, String currentUserId) {
        if (order == null || !StringUtils.hasText(currentUserId)) {
            return false;
        }
        
        // 检查订单的用户ID或代理ID是否匹配
        return !currentUserId.equals(order.getUserId()) &&
                !currentUserId.equals(order.getAgent());
    }
    
    /**
     * 验证订单是否可以结算
     * 
     * @param order 订单
     */
    public void validateOrderCanSettle(SysOrder order) {
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        if (order.getOrderStatus() == null) {
            throw new BusinessException("订单状态异常");
        }
        
        // 只有待结算状态的订单才能进行结算
        if (order.getOrderStatus() != 0) {
            throw new BusinessException("订单状态不允许结算，当前状态: " + order.getOrderStatus());
        }
    }
    
    /**
     * 验证订单是否可以取消
     * 
     * @param order 订单
     */
    public void validateOrderCanCancel(SysOrder order) {
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        if (order.getOrderStatus() == null) {
            throw new BusinessException("订单状态异常");
        }
        
        // 只有待结算状态的订单才能取消
        if (order.getOrderStatus() != 0) {
            throw new BusinessException("订单状态不允许取消，当前状态: " + order.getOrderStatus());
        }
    }
}
