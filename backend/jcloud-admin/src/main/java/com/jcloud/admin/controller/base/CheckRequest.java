package com.jcloud.admin.controller.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 检查请求参数封装类
 * 用于封装检查方法的多个参数，避免参数过多的问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckRequest {
    
    /**
     * 要检查的值
     */
    private String value;
    
    /**
     * 排除的ID（编辑时使用）
     */
    private Long excludeId;
    
    /**
     * 父级ID（层级结构时使用）
     */
    private Long parentId;
    
    /**
     * 权限标识
     */
    private String permission;
    
    /**
     * 项目名称（用于错误消息）
     */
    private String itemName;
    
    /**
     * 检查类型
     */
    private CheckType checkType;
    
    /**
     * 检查类型枚举
     */
    public enum CheckType {
        CODE("编码"),
        NAME("名称"),
        PATH("路径"),
        PHONE("手机号"),
        EMAIL("邮箱"),
        PERMISSION_CODE("权限编码");
        
        private final String description;
        
        CheckType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建编码检查请求
     */
    public static CheckRequest forCode(String code, Long excludeId) {
        return CheckRequest.builder()
            .value(code)
            .excludeId(excludeId)
            .checkType(CheckType.CODE)
            .itemName("编码")
            .build();
    }
    
    /**
     * 创建名称检查请求
     */
    public static CheckRequest forName(String name, Long excludeId) {
        return CheckRequest.builder()
            .value(name)
            .excludeId(excludeId)
            .checkType(CheckType.NAME)
            .itemName("名称")
            .build();
    }
    
    /**
     * 创建带父级的名称检查请求
     */
    public static CheckRequest forNameWithParent(String name, Long parentId, Long excludeId) {
        return CheckRequest.builder()
            .value(name)
            .parentId(parentId)
            .excludeId(excludeId)
            .checkType(CheckType.NAME)
            .itemName("名称")
            .build();
    }
    
    /**
     * 创建路径检查请求
     */
    public static CheckRequest forPath(String path, Long excludeId) {
        return CheckRequest.builder()
            .value(path)
            .excludeId(excludeId)
            .checkType(CheckType.PATH)
            .itemName("路径")
            .build();
    }
    
    /**
     * 创建手机号检查请求
     */
    public static CheckRequest forPhone(String phone, Long excludeId) {
        return CheckRequest.builder()
            .value(phone)
            .excludeId(excludeId)
            .checkType(CheckType.PHONE)
            .itemName("手机号")
            .build();
    }
    
    /**
     * 创建邮箱检查请求
     */
    public static CheckRequest forEmail(String email, Long excludeId) {
        return CheckRequest.builder()
            .value(email)
            .excludeId(excludeId)
            .checkType(CheckType.EMAIL)
            .itemName("邮箱")
            .build();
    }
    
    /**
     * 创建权限编码检查请求
     */
    public static CheckRequest forPermissionCode(String permissionCode, Long excludeId) {
        return CheckRequest.builder()
            .value(permissionCode)
            .excludeId(excludeId)
            .checkType(CheckType.PERMISSION_CODE)
            .itemName("权限编码")
            .build();
    }
    
    /**
     * 设置权限标识
     */
    public CheckRequest withPermission(String permission) {
        this.permission = permission;
        return this;
    }
    
    /**
     * 设置自定义项目名称
     */
    public CheckRequest withItemName(String itemName) {
        this.itemName = itemName;
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return value != null && !value.trim().isEmpty() && checkType != null;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return itemName != null ? itemName : checkType.getDescription();
    }
}
