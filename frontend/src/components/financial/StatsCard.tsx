import React from 'react'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { cn } from '@/utils'
import { formatPercentage, smartFormatValue } from '@/utils/financial'
import { FINANCIAL_RESPONSIVE_CONFIG } from '@/constants/responsive'
import type { StatsCardData } from '@/types/financial'

/**
 * 统计卡片组件属性
 */
export interface StatsCardProps {
  /** 卡片数据 */
  data?: StatsCardData
  /** 标题 */
  title?: string
  /** 值 */
  value?: number | string
  /** 单位 */
  unit?: string
  /** 数据类型，用于自动格式化 */
  valueType?: 'currency' | 'percentage' | 'number' | 'integer'
  /** 图标 */
  icon?: React.ReactNode
  /** 颜色主题 */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 是否显示加载状态 */
  loading?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 点击事件 */
  onClick?: () => void
}

/**
 * 获取颜色主题样式
 */
const getColorStyles = (color: StatsCardProps['color']) => {
  switch (color) {
    case 'success':
      return 'border-green-200 bg-green-50 text-green-900'
    case 'warning':
      return 'border-yellow-200 bg-yellow-50 text-yellow-900'
    case 'danger':
      return 'border-red-200 bg-red-50 text-red-900'
    case 'info':
      return 'border-blue-200 bg-blue-50 text-blue-900'
    case 'primary':
    default:
      return 'border-primary/20 bg-primary/5 text-primary'
  }
}

/**
 * 加载状态骨架屏
 */
const LoadingSkeleton: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded mb-2"></div>
    <div className="h-8 bg-gray-200 rounded mb-1"></div>
    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
  </div>
)

/**
 * 空数据占位符
 */
const EmptyPlaceholder: React.FC<{ title?: string }> = ({ title }) => (
  <div className="text-center text-gray-500">
    <div className="text-sm mb-1">{title || '暂无数据'}</div>
    <div className="text-2xl font-semibold text-gray-300">--</div>
  </div>
)

/**
 * 统计卡片组件
 * 用于展示单个统计指标，支持不同数据类型的格式化显示
 */
export const StatsCard: React.FC<StatsCardProps> = ({
  data,
  title,
  value,
  unit,
  valueType = 'number',
  icon,
  color = 'primary',
  loading = false,
  className,
  onClick
}) => {
  // 优先使用data中的数据，其次使用直接传入的props
  const cardTitle = data?.title || title
  const cardValue = data?.value ?? value
  const cardUnit = data?.unit || unit
  const cardIcon = icon
  const cardColor = data?.color || color

  // 格式化显示值
  const getFormattedValue = (): string => {
    if (cardValue === null || cardValue === undefined || cardValue === '') {
      return '--'
    }

    // 如果data中已有格式化值，直接使用
    if (data?.formattedValue) {
      return data.formattedValue
    }

    // 根据类型自动格式化，传递统计项名称用于特殊处理
    return smartFormatValue(cardValue, valueType, cardTitle)
  }

  const formattedValue = getFormattedValue()

  return (
    <Card
      className={cn(
        'transition-all duration-200 hover:shadow-md',
        getColorStyles(cardColor),
        onClick && 'cursor-pointer hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      <CardHeader className={cn(
        "flex flex-row items-center justify-between space-y-0 pb-2",
        FINANCIAL_RESPONSIVE_CONFIG.statsCard.padding
      )}>
        <CardTitle className={cn(
          "font-medium text-muted-foreground",
          FINANCIAL_RESPONSIVE_CONFIG.statsCard.titleSize
        )}>
          {cardTitle}
        </CardTitle>
        {cardIcon && (
          <div className={cn(
            "text-muted-foreground",
            FINANCIAL_RESPONSIVE_CONFIG.statsCard.iconSize
          )}>
            {cardIcon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <LoadingSkeleton />
        ) : cardValue === null || cardValue === undefined || cardValue === '' ? (
          <EmptyPlaceholder title={cardTitle} />
        ) : (
          <div>
            <div className={cn(
              "font-bold",
              FINANCIAL_RESPONSIVE_CONFIG.statsCard.valueSize
            )}>
              {formattedValue}
            </div>
            {cardUnit && !data?.formattedValue && (
              <p className={cn(
                "text-muted-foreground mt-1",
                FINANCIAL_RESPONSIVE_CONFIG.statsCard.titleSize
              )}>
                单位: {cardUnit}
              </p>
            )}
            {data?.trend && (
              <div className={cn(
                "flex items-center mt-2",
                FINANCIAL_RESPONSIVE_CONFIG.statsCard.titleSize
              )}>
                <span
                  className={cn(
                    'flex items-center',
                    data.trend.direction === 'up' && 'text-green-600',
                    data.trend.direction === 'down' && 'text-red-600',
                    data.trend.direction === 'stable' && 'text-gray-600'
                  )}
                >
                  {data.trend.direction === 'up' && <TrendingUp className={cn(
                    FINANCIAL_RESPONSIVE_CONFIG.statsCard.iconSize
                  )} />}
                  {data.trend.direction === 'down' && <TrendingDown className={cn(
                    FINANCIAL_RESPONSIVE_CONFIG.statsCard.iconSize
                  )} />}
                  {data.trend.direction === 'stable' && <Minus className={cn(
                    FINANCIAL_RESPONSIVE_CONFIG.statsCard.iconSize
                  )} />}
                  <span className="ml-1">
                    {formatPercentage(data.trend.percentage / 100)}
                  </span>
                </span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 统计卡片网格组件
 * 用于展示多个统计卡片的网格布局
 */
export interface StatsCardGridProps {
  /** 卡片数据列表 */
  cards: StatsCardData[]
  /** 网格列数 */
  columns?: 1 | 2 | 3 | 4 | 6
  /** 是否显示加载状态 */
  loading?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 卡片点击事件 */
  onCardClick?: (card: StatsCardData, index: number) => void
}

export const StatsCardGrid: React.FC<StatsCardGridProps> = ({
  cards,
  columns = 4,
  loading = false,
  className,
  onCardClick
}) => {
  // 使用响应式配置的网格布局
  const getGridCols = (cols: number): string => {
    switch (cols) {
      case 1:
        return 'grid-cols-1'
      case 2:
        return 'grid-cols-1 sm:grid-cols-2'
      case 3:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
      case 4:
        return FINANCIAL_RESPONSIVE_CONFIG.statsCard.gridCols
      case 6:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
      default:
        return FINANCIAL_RESPONSIVE_CONFIG.statsCard.gridCols
    }
  }

  return (
    <div className={cn(
      'grid',
      FINANCIAL_RESPONSIVE_CONFIG.statsCard.gap,
      getGridCols(columns),
      className
    )}>
      {cards.map((card, index) => (
        <StatsCard
          key={`${card.title}-${index}`}
          data={card}
          loading={loading}
          onClick={() => onCardClick?.(card, index)}
        />
      ))}
    </div>
  )
}

export default StatsCard