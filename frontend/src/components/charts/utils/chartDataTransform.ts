import { format, parseISO, subDays, isValid } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { 
  GroupedFinancialStatsResponse, 
  FinancialStatsItem 
} from '@/types/financial'

/**
 * 图表数据转换工具函数
 */

/**
 * 时间序列数据点接口
 */
export interface TimeSeriesDataPoint {
  date: string
  timestamp: number
  value: number
  label?: string
  category?: string
}

/**
 * 饼图数据点接口
 */
export interface PieChartDataPoint {
  name: string
  value: number
  percentage: number
  color?: string
  label?: string
}

/**
 * 柱状图数据点接口
 */
export interface BarChartDataPoint {
  name: string
  value: number
  category?: string
  color?: string
  label?: string
}

/**
 * 散点图数据点接口
 */
export interface ScatterDataPoint {
  x: number
  y: number
  z?: number
  name?: string
  category?: string
  color?: string
}

/**
 * 热力图数据点接口
 */
export interface HeatmapDataPoint {
  x: string | number
  y: string | number
  value: number
  label?: string
}

/**
 * 转换财务统计数据为时间序列数据
 */
export const transformToTimeSeriesData = (
  data: FinancialStatsItem[],
  dateField: string = 'date',
  valueField: string = 'statValue'
): TimeSeriesDataPoint[] => {
  if (!data || !Array.isArray(data)) {
    return []
  }

  return data
    .map(item => {
      const dateValue = item[dateField as keyof FinancialStatsItem]
      const value = Number(item[valueField as keyof FinancialStatsItem]) || 0
      
      let timestamp: number
      let formattedDate: string

      if (typeof dateValue === 'string') {
        const parsedDate = parseISO(dateValue)
        if (isValid(parsedDate)) {
          timestamp = parsedDate.getTime()
          formattedDate = format(parsedDate, 'MM-dd', { locale: zhCN })
        } else {
          // 如果解析失败，尝试作为时间戳处理
          timestamp = Number(dateValue) || Date.now()
          formattedDate = format(new Date(timestamp), 'MM-dd', { locale: zhCN })
        }
      } else if (typeof dateValue === 'number') {
        timestamp = dateValue
        formattedDate = format(new Date(timestamp), 'MM-dd', { locale: zhCN })
      } else {
        timestamp = Date.now()
        formattedDate = format(new Date(), 'MM-dd', { locale: zhCN })
      }

      return {
        date: formattedDate,
        timestamp,
        value,
        label: item.statName,
        category: item.category
      }
    })
    .sort((a, b) => a.timestamp - b.timestamp)
}

/**
 * 转换财务统计数据为饼图数据
 */
export const transformToPieChartData = (
  data: FinancialStatsItem[],
  nameField: string = 'statName',
  valueField: string = 'statValue'
): PieChartDataPoint[] => {
  if (!data || !Array.isArray(data)) {
    return []
  }

  const total = data.reduce((sum, item) => {
    return sum + (Number(item[valueField as keyof FinancialStatsItem]) || 0)
  }, 0)

  return data
    .map(item => {
      const name = String(item[nameField as keyof FinancialStatsItem] || '未知')
      const value = Number(item[valueField as keyof FinancialStatsItem]) || 0
      const percentage = total > 0 ? (value / total) * 100 : 0

      return {
        name,
        value,
        percentage: Math.round(percentage * 100) / 100,
        label: `${name}: ${value.toLocaleString()}`
      }
    })
    .filter(item => item.value > 0)
    .sort((a, b) => b.value - a.value)
}

/**
 * 转换财务统计数据为柱状图数据
 */
export const transformToBarChartData = (
  data: FinancialStatsItem[],
  nameField: string = 'statName',
  valueField: string = 'statValue',
  categoryField?: string
): BarChartDataPoint[] => {
  if (!data || !Array.isArray(data)) {
    return []
  }

  return data
    .map(item => {
      const name = String(item[nameField as keyof FinancialStatsItem] || '未知')
      const value = Number(item[valueField as keyof FinancialStatsItem]) || 0
      const category = categoryField ? 
        String(item[categoryField as keyof FinancialStatsItem] || '其他') : 
        undefined

      return {
        name,
        value,
        category,
        label: `${name}: ${value.toLocaleString()}`
      }
    })
    .filter(item => item.value > 0)
    .sort((a, b) => b.value - a.value)
}

/**
 * 转换分组财务数据为多系列时间序列
 */
export const transformGroupedDataToMultiSeries = (
  data: GroupedFinancialStatsResponse
): Record<string, TimeSeriesDataPoint[]> => {
  const result: Record<string, TimeSeriesDataPoint[]> = {}

  if (data.userStats) {
    result['用户相关'] = transformToTimeSeriesData(data.userStats)
  }
  
  if (data.anchorStats) {
    result['主播相关'] = transformToTimeSeriesData(data.anchorStats)
  }
  
  if (data.totalStats) {
    result['合计统计'] = transformToTimeSeriesData(data.totalStats)
  }
  
  if (data.businessStats) {
    result['其他业务'] = transformToTimeSeriesData(data.businessStats)
  }

  return result
}

/**
 * 生成模拟时间序列数据
 */
export const generateMockTimeSeriesData = (
  days: number = 30,
  baseValue: number = 1000,
  variance: number = 0.3
): TimeSeriesDataPoint[] => {
  const data: TimeSeriesDataPoint[] = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = subDays(now, i)
    const randomFactor = 1 + (Math.random() - 0.5) * variance
    const value = Math.round(baseValue * randomFactor)

    data.push({
      date: format(date, 'MM-dd', { locale: zhCN }),
      timestamp: date.getTime(),
      value,
      label: format(date, 'yyyy-MM-dd', { locale: zhCN })
    })
  }

  return data
}

/**
 * 数据聚合工具
 */
export const aggregateData = {
  /**
   * 按日期聚合数据
   */
  byDate: (data: TimeSeriesDataPoint[]): TimeSeriesDataPoint[] => {
    const grouped = data.reduce((acc, item) => {
      const dateKey = item.date
      if (!acc[dateKey]) {
        acc[dateKey] = { ...item, value: 0 }
      }
      acc[dateKey].value += item.value
      return acc
    }, {} as Record<string, TimeSeriesDataPoint>)

    return Object.values(grouped).sort((a, b) => a.timestamp - b.timestamp)
  },

  /**
   * 按分类聚合数据
   */
  byCategory: (data: BarChartDataPoint[]): BarChartDataPoint[] => {
    const grouped = data.reduce((acc, item) => {
      const categoryKey = item.category || '其他'
      if (!acc[categoryKey]) {
        acc[categoryKey] = { name: categoryKey, value: 0, category: categoryKey }
      }
      acc[categoryKey].value += item.value
      return acc
    }, {} as Record<string, BarChartDataPoint>)

    return Object.values(grouped).sort((a, b) => b.value - a.value)
  }
}

/**
 * 数据格式化工具
 */
export const formatters = {
  /**
   * 格式化货币
   */
  currency: (value: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(value)
  },

  /**
   * 格式化数字
   */
  number: (value: number): string => {
    return new Intl.NumberFormat('zh-CN').format(value)
  },

  /**
   * 格式化百分比
   */
  percentage: (value: number): string => {
    return `${(value * 100).toFixed(1)}%`
  },

  /**
   * 格式化日期
   */
  date: (value: string | number | Date): string => {
    const date = typeof value === 'string' ? parseISO(value) : new Date(value)
    return format(date, 'yyyy-MM-dd', { locale: zhCN })
  },

  /**
   * 格式化时间
   */
  time: (value: string | number | Date): string => {
    const date = typeof value === 'string' ? parseISO(value) : new Date(value)
    return format(date, 'HH:mm', { locale: zhCN })
  }
}
