/**
 * 图表主题配置
 * 提供统一的颜色、字体、样式配置
 */

/**
 * 主题颜色配置
 */
export const CHART_COLORS = {
  // 主色调
  primary: '#3b82f6',
  secondary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  
  // 扩展色板 - 用于多系列图表
  palette: [
    '#3b82f6', // blue
    '#10b981', // emerald
    '#f59e0b', // amber
    '#ef4444', // red
    '#8b5cf6', // violet
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316', // orange
    '#ec4899', // pink
    '#6366f1', // indigo
    '#14b8a6', // teal
    '#a855f7', // purple
  ],
  
  // 渐变色配置
  gradients: {
    primary: ['#3b82f6', '#1d4ed8'],
    success: ['#10b981', '#047857'],
    warning: ['#f59e0b', '#d97706'],
    danger: ['#ef4444', '#dc2626'],
    info: ['#06b6d4', '#0891b2'],
  },
  
  // 背景色
  background: {
    light: '#ffffff',
    dark: '#1f2937',
    muted: '#f9fafb',
  },
  
  // 文本颜色
  text: {
    primary: '#111827',
    secondary: '#6b7280',
    muted: '#9ca3af',
    inverse: '#ffffff',
  },
  
  // 网格线颜色
  grid: {
    light: '#f3f4f6',
    dark: '#374151',
  },
  
  // 边框颜色
  border: {
    light: '#e5e7eb',
    dark: '#4b5563',
  }
}

/**
 * 字体配置
 */
export const CHART_FONTS = {
  family: {
    sans: 'Inter, system-ui, sans-serif',
    mono: 'JetBrains Mono, Consolas, monospace',
  },
  size: {
    xs: 10,
    sm: 12,
    base: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
  },
  weight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  }
}

/**
 * 图表尺寸配置
 */
export const CHART_SIZES = {
  // 默认高度
  height: {
    sm: 200,
    md: 300,
    lg: 400,
    xl: 500,
    '2xl': 600,
  },
  
  // 边距配置
  margin: {
    sm: { top: 10, right: 10, bottom: 10, left: 10 },
    md: { top: 20, right: 20, bottom: 20, left: 20 },
    lg: { top: 30, right: 30, bottom: 30, left: 30 },
  },
  
  // 图例配置
  legend: {
    height: 40,
    iconSize: 14,
    fontSize: 12,
  },
  
  // 工具提示配置
  tooltip: {
    maxWidth: 300,
    fontSize: 12,
    padding: 12,
  }
}

/**
 * 动画配置
 */
export const CHART_ANIMATIONS = {
  // 进入动画持续时间
  duration: {
    fast: 200,
    normal: 400,
    slow: 800,
  },
  
  // 缓动函数
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
  
  // 默认动画配置
  default: {
    animationBegin: 0,
    animationDuration: 400,
    animationEasing: 'ease-out',
  }
}

/**
 * 响应式断点配置
 */
export const CHART_BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
}

/**
 * 获取响应式图表高度
 */
export const getResponsiveHeight = (): number => {
  const width = window.innerWidth
  
  if (width < CHART_BREAKPOINTS.sm) {
    return CHART_SIZES.height.sm
  } else if (width < CHART_BREAKPOINTS.md) {
    return CHART_SIZES.height.md
  } else if (width < CHART_BREAKPOINTS.lg) {
    return CHART_SIZES.height.lg
  } else {
    return CHART_SIZES.height.xl
  }
}

/**
 * 获取图表颜色
 */
export const getChartColor = (index: number): string => {
  return CHART_COLORS.palette[index % CHART_COLORS.palette.length]
}

/**
 * 获取渐变色定义
 */
export const getGradientDef = (id: string, colors: string[]) => ({
  id,
  type: 'linear',
  x1: 0,
  y1: 0,
  x2: 0,
  y2: 1,
  stops: colors.map((color, index) => ({
    offset: `${(index / (colors.length - 1)) * 100}%`,
    stopColor: color,
    stopOpacity: 1 - (index * 0.3)
  }))
})

/**
 * 默认图表主题配置
 */
export const DEFAULT_CHART_THEME = {
  colors: CHART_COLORS,
  fonts: CHART_FONTS,
  sizes: CHART_SIZES,
  animations: CHART_ANIMATIONS,
  breakpoints: CHART_BREAKPOINTS,
}

export default DEFAULT_CHART_THEME
