import React from 'react'
import { ResponsiveContainer } from 'recharts'
import { cn } from '@/utils'

/**
 * 图表容器属性接口
 */
export interface ChartContainerProps {
  /** 子组件 */
  children: React.ReactNode
  /** 容器高度 */
  height?: number | string
  /** 容器宽度 */
  width?: number | string
  /** 自定义类名 */
  className?: string
  /** 是否启用响应式 */
  responsive?: boolean
  /** 最小高度 */
  minHeight?: number
  /** 最小宽度 */
  minWidth?: number
  /** 容器样式 */
  style?: React.CSSProperties
  /** 是否显示调试信息 */
  debug?: boolean
}

/**
 * 图表容器组件
 * 提供响应式容器和统一的图表包装
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({
  children,
  height = 400,
  width = '100%',
  className,
  responsive = true,
  minHeight = 200,
  minWidth = 300,
  style,
  debug = false,
  ...props
}) => {
  // 如果启用响应式，使用ResponsiveContainer
  if (responsive) {
    return (
      <div 
        className={cn("w-full", className)}
        style={{
          height: `${height}px`,
          minHeight: `${minHeight}px`,
          minWidth: `${minWidth}px`,
          ...style
        }}
        {...props}
      >
        <ResponsiveContainer
          width="100%"
          height="100%"
          minHeight={minHeight}
          minWidth={minWidth}
          debounce={50}
        >
          {children as React.ReactElement}
        </ResponsiveContainer>
        {debug && (
          <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            {`${height}px`} × {width}
          </div>
        )}
      </div>
    )
  }

  // 非响应式容器
  return (
    <div 
      className={cn("relative", className)}
      style={{
        height: `${height}px`,
        width: width,
        minHeight: `${minHeight}px`,
        minWidth: `${minWidth}px`,
        ...style
      }}
      {...props}
    >
      {children}
      {debug && (
        <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
          {`${height}px`} × {width}
        </div>
      )}
    </div>
  )
}

export default ChartContainer
