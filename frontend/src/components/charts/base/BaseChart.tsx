import React, { forwardRef, useMemo } from 'react'
import { cn } from '@/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/Button'

/**
 * 基础图表属性接口
 */
export interface BaseChartProps {
  /** 图表标题 */
  title?: string
  /** 图表描述 */
  description?: string
  /** 是否显示加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 是否显示刷新按钮 */
  showRefresh?: boolean
  /** 刷新回调函数 */
  onRefresh?: () => void
  /** 图表高度 */
  height?: number | string
  /** 自定义类名 */
  className?: string
  /** 子组件 */
  children?: React.ReactNode
  /** 是否显示边框 */
  bordered?: boolean
  /** 图表容器样式 */
  containerStyle?: React.CSSProperties
  /** 头部额外内容 */
  headerExtra?: React.ReactNode
  /** 是否可折叠 */
  collapsible?: boolean
  /** 默认是否折叠 */
  defaultCollapsed?: boolean
}

/**
 * 基础图表组件
 * 提供统一的图表容器、加载状态、错误处理等功能
 */
export const BaseChart = forwardRef<HTMLDivElement, BaseChartProps>(({
  title,
  description,
  loading = false,
  error = null,
  showRefresh = false,
  onRefresh,
  height = 400,
  className,
  children,
  bordered = true,
  containerStyle,
  headerExtra,
  collapsible = false,
  defaultCollapsed = false,
  ...props
}, ref) => {
  const [collapsed, setCollapsed] = React.useState(defaultCollapsed)

  // 计算图表容器样式
  const chartStyle = useMemo(() => ({
    height: typeof height === 'number' ? `${height}px` : height,
    ...containerStyle
  }), [height, containerStyle])

  // 渲染头部内容
  const renderHeader = () => {
    if (!title && !description && !showRefresh && !headerExtra) {
      return null
    }

    return (
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div className="space-y-1">
          {title && (
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              {title}
              {collapsible && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCollapsed(!collapsed)}
                  className="h-6 w-6 p-0"
                >
                  <RefreshCw 
                    className={cn(
                      "h-4 w-4 transition-transform",
                      collapsed && "rotate-180"
                    )} 
                  />
                </Button>
              )}
            </CardTitle>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {headerExtra}
          {showRefresh && onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
              className="h-8"
            >
              <RefreshCw className={cn(
                "h-4 w-4",
                loading && "animate-spin"
              )} />
            </Button>
          )}
        </div>
      </CardHeader>
    )
  }

  // 渲染内容区域
  const renderContent = () => {
    if (collapsed) {
      return null
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <p className="text-sm text-muted-foreground mb-4">
            {error}
          </p>
          {showRefresh && onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={cn(
                "h-4 w-4 mr-2",
                loading && "animate-spin"
              )} />
              重试
            </Button>
          )}
        </div>
      )
    }

    if (loading) {
      return (
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-32 w-full" />
          <div className="flex gap-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-1/5" />
          </div>
        </div>
      )
    }

    return (
      <div style={chartStyle} className="w-full">
        {children}
      </div>
    )
  }

  return (
    <Card 
      ref={ref}
      className={cn(
        "w-full",
        !bordered && "border-none shadow-none",
        className
      )}
      {...props}
    >
      {renderHeader()}
      <CardContent className={cn(
        "pt-0",
        collapsed && "pb-4"
      )}>
        {renderContent()}
      </CardContent>
    </Card>
  )
})

BaseChart.displayName = 'BaseChart'

export default BaseChart
