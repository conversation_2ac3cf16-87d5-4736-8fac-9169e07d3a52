import React, { useMemo } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts'
import { type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '@/components/charts'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

/**
 * 多维度收入数据接口 - 只包含需要显示的业务类型
 */
export interface RevenueDataPoint {
  /** 日期 */
  date: string
  /** 充值收入 */
  rechargeRevenue: number
  /** 开箱收入 */
  openBoxRevenue: number
  /** 兑换收入 */
  exchangeRevenue: number
  /** 对战收入 */
  battleRevenue: number
  /** 时间戳 */
  timestamp?: number
}

/**
 * 多维度收入趋势图属性接口
 */
export interface RevenueMultiLineChartProps extends Omit<BaseChartProps, 'children'> {
  /** 收入数据 */
  data: RevenueDataPoint[]
  /** 显示的数据线配置 */
  lines?: {
    rechargeRevenue?: boolean
    openBoxRevenue?: boolean
    exchangeRevenue?: boolean
    battleRevenue?: boolean
  }
  /** 是否显示数据点 */
  showDots?: boolean
  /** 是否显示网格 */
  showGrid?: boolean
  /** 是否显示趋势指标 */
  showTrend?: boolean
  /** 图表高度 */
  height?: number
  /** 自定义颜色配置 */
  colors?: {
    rechargeRevenue?: string
    openBoxRevenue?: string
    exchangeRevenue?: string
    battleRevenue?: string
  }
}

/**
 * 默认线条配置 - 只显示有实际数据的业务类型
 */
const DEFAULT_LINES = {
  rechargeRevenue: true,      // 充值收入
  openBoxRevenue: true,       // 开箱收入
  exchangeRevenue: true,      // 兑换收入
  battleRevenue: true,        // 对战收入
  tianxuanRevenue: false,     // 天选收入 - 不显示
  upgradeRevenue: false,      // 升级收入 - 不显示
  rollRoomRevenue: false,     // Roll房收入 - 不显示
  fusionRevenue: false,       // 融合收入 - 不显示
  rewardRevenue: false,       // 奖励收入 - 不显示
  compensationRevenue: false, // 补偿收入 - 不显示
  commissionExpense: false,   // 佣金支出 - 不显示
  netRevenue: false          // 净收入 - 不显示
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = {
  rechargeRevenue: '#3b82f6',    // 蓝色 - 总充值
  openBoxRevenue: '#10b981',     // 绿色 - 开箱
  exchangeRevenue: '#f59e0b',    // 橙色 - 兑换
  battleRevenue: '#ef4444',      // 红色 - 对战
  tianxuanRevenue: '#8b5cf6',    // 紫色 - 天选
  upgradeRevenue: '#06b6d4',     // 青色 - 升级
  rollRoomRevenue: '#84cc16',    // 绿黄 - Roll房
  fusionRevenue: '#f97316',      // 深橙 - 融合
  rewardRevenue: '#ec4899',      // 粉色 - 奖励
  compensationRevenue: '#6b7280', // 灰色 - 补偿
  commissionExpense: '#fbbf24',  // 黄色 - 佣金支出
  netRevenue: '#1f2937'          // 深灰 - 净收入
}

/**
 * 线条配置
 */
const LINE_CONFIG = [
  {
    key: 'rechargeRevenue',
    name: '充值收入',
    strokeWidth: 3,
    dot: { r: 5 },
    activeDot: { r: 7 }
  },
  {
    key: 'openBoxRevenue',
    name: '开箱收入',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 }
  },
  {
    key: 'exchangeRevenue',
    name: '兑换收入',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 }
  },
  {
    key: 'battleRevenue',
    name: '对战收入',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 }
  },


]

/**
 * 多维度收入趋势图组件
 */
export const RevenueMultiLineChart: React.FC<RevenueMultiLineChartProps> = ({
  data = [],
  lines = DEFAULT_LINES,
  showDots = true,
  showGrid = true,
  showTrend = true,
  height = 400,
  colors = DEFAULT_COLORS,
}) => {
  // 计算趋势数据
  const trendData = useMemo(() => {
    if (!data || data.length < 2) {
      return null
    }

    const latest = data[data.length - 1]
    const previous = data[data.length - 2]

    const calculateTrend = (current: number, prev: number) => {
      if (prev === 0) return { direction: 'stable' as const, change: 0, percentage: 0 }
      const change = current - prev
      const percentage = (change / prev) * 100
      const direction = change > 0 ? 'up' as const : change < 0 ? 'down' as const : 'stable' as const
      return { direction, change, percentage }
    }

    return {
      rechargeRevenue: calculateTrend(latest.rechargeRevenue, previous.rechargeRevenue),
      openBoxRevenue: calculateTrend(latest.openBoxRevenue, previous.openBoxRevenue),
      exchangeRevenue: calculateTrend(latest.exchangeRevenue, previous.exchangeRevenue),
      battleRevenue: calculateTrend(latest.battleRevenue, previous.battleRevenue)
    }
  }, [data])

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3 min-w-[200px]">
        <p className="font-medium text-sm mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between text-sm mb-1">
            <span className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}
            </span>
            <span className="font-medium">
              {formatters.currency(entry.value)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  // 渲染趋势指标
  const renderTrendIndicators = () => {
    if (!showTrend || !trendData) {
      return null
    }

    const indicators = [
      { key: 'rechargeRevenue', label: '充值收入', color: colors.rechargeRevenue },
      { key: 'openBoxRevenue', label: '开箱收入', color: colors.openBoxRevenue },
      { key: 'exchangeRevenue', label: '兑换收入', color: colors.exchangeRevenue },
      { key: 'battleRevenue', label: '对战收入', color: colors.battleRevenue }
    ]

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {indicators.map(({ key, label, color }) => {
          const trend = trendData[key as keyof typeof trendData]
          const TrendIcon = trend.direction === 'up' ? TrendingUp : 
                           trend.direction === 'down' ? TrendingDown : Minus

          return (
            <div key={key} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: color }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-xs text-muted-foreground truncate">{label}</p>
                <div className="flex items-center gap-1">
                  <TrendIcon 
                    className={`w-3 h-3 ${
                      trend.direction === 'up' ? 'text-green-500' :
                      trend.direction === 'down' ? 'text-red-500' : 'text-gray-500'
                    }`}
                  />
                  <span className="text-xs font-medium">
                    {Math.abs(trend.percentage).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          dataKey="date"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={(value) => formatters.currency(value)}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          wrapperStyle={{ paddingTop: '20px' }}
          iconType="line"
        />
        
        {LINE_CONFIG.map((config) => {
          const isVisible = lines[config.key as keyof typeof lines]
          if (!isVisible) return null

          return (
            <Line
              key={config.key}
              type="monotone"
              dataKey={config.key}
              name={config.name}
              stroke={colors[config.key as keyof typeof colors]}
              strokeWidth={config.strokeWidth}
              strokeDasharray={(config as any).strokeDasharray}
              dot={showDots ? config.dot : false}
              activeDot={config.activeDot}
              animationDuration={CHART_ANIMATIONS.default.animationDuration}
            />
          )
        })}
      </LineChart>
    </ChartContainer>
  )

  return (
    <div className="space-y-4">
      {renderTrendIndicators()}
      {renderChart()}
    </div>
  )
}

export default RevenueMultiLineChart
