import { useState } from "react"
import { addDays, format, startOfMonth, endOfMonth, subMonths, startOfDay, endOfDay, setHours, setMinutes, setSeconds } from "date-fns"
import { zhCN } from "date-fns/locale"
import { CalendarIcon, ChevronDown } from "lucide-react"
import type { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "./Button"
import { Calendar } from "./calendar"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Card } from "./Card"
import { Separator } from "./separator"
import { TimePicker } from "./time-picker"
import { Label } from "./label"
import { useBreakpoint } from "@/hooks/useBreakpoint"

interface DateRangePickerProps {
    className?: string
    onDateChange?: (range: DateRange | undefined) => void
    enableTime?: boolean // 新增：是否启用时间选择
    timeFormat?: string // 新增：时间格式
}

// 自定义时间设置函数
const customStartOfDay = (date: Date): Date => {
    // 设置为当天 00:00:00
    return setSeconds(setMinutes(setHours(date, 0), 0), 0)
}

const customEndOfDay = (date: Date): Date => {
    // 设置为当天 23:59:59
    return setSeconds(setMinutes(setHours(date, 23), 59), 59)
}

const quickOptions = [
    {
        label: "今天",
        getValue: () => ({
            from: customStartOfDay(new Date()),
            to: customEndOfDay(new Date()),
        }),
    },
    {
        label: "昨天",
        getValue: () => ({
            from: customStartOfDay(addDays(new Date(), -1)),
            to: customEndOfDay(addDays(new Date(), -1)),
        }),
    },
    {
        label: "过去7天",
        getValue: () => ({
            from: customStartOfDay(addDays(new Date(), -6)),
            to: customEndOfDay(new Date()),
        }),
    },
    {
        label: "过去30天",
        getValue: () => ({
            from: customStartOfDay(addDays(new Date(), -29)),
            to: customEndOfDay(new Date()),
        }),
    },
    {
        label: "本月",
        getValue: () => ({
            from: customStartOfDay(startOfMonth(new Date())),
            to: customEndOfDay(endOfMonth(new Date())),
        }),
    },
    {
        label: "上个月",
        getValue: () => {
            const lastMonth = subMonths(new Date(), 1)
            return {
                from: customStartOfDay(startOfMonth(lastMonth)),
                to: customEndOfDay(endOfMonth(lastMonth)),
            }
        },
    },
]

export default function DateRangePicker({
    className,
    onDateChange,
    enableTime = false,
    timeFormat = "HH:mm:ss"
}: DateRangePickerProps) {
    const [date, setDate] = useState<DateRange | undefined>({
        from: customStartOfDay(addDays(new Date(), -29)),
        to: customEndOfDay(new Date()),
    })
    const [open, setOpen] = useState(false)
    const { isMobile, isTablet } = useBreakpoint()

    // 时间状态 - 设置默认时间
    const [startTime, setStartTime] = useState<Date>(() => {
        // 开始时间默认为当天 00:00:00
        const now = new Date()
        return setSeconds(setMinutes(setHours(now, 0), 0), 0)
    })
    const [endTime, setEndTime] = useState<Date>(() => {
        // 结束时间默认为当天 23:59:59
        const now = new Date()
        return setSeconds(setMinutes(setHours(now, 23), 59), 59)
    })

    const handleDateChange = (newDate: DateRange | undefined) => {
        if (enableTime && newDate) {
            // 如果启用时间选择，需要合并日期和时间，但不立即触发回调
            const updatedRange: DateRange = {
                from: newDate.from ? combineDateTime(newDate.from, startTime) : undefined,
                to: newDate.to ? combineDateTime(newDate.to, endTime) : undefined,
            }
            setDate(updatedRange)
            // 注意：这里不调用 onDateChange，只有确认时才调用
        } else {
            setDate(newDate)
            // 如果没有启用时间选择，保持原有行为（立即触发）
            onDateChange?.(newDate)
        }
    }

    const handleQuickSelect = (range: DateRange) => {
        if (enableTime && range?.from && range?.to) {
            // 如果启用时间选择，合并当前时间设置并立即触发回调
            const updatedRange: DateRange = {
                from: combineDateTime(range.from, startTime),
                to: combineDateTime(range.to, endTime),
            }
            setDate(updatedRange)
            // 快捷选择后立即触发回调
            onDateChange?.(updatedRange)
        } else {
            // 如果没有启用时间选择，保持原有行为（立即触发）
            handleDateChange(range)
        }
        setOpen(false)
    }

    // 合并日期和时间的辅助函数
    const combineDateTime = (date: Date, time: Date): Date => {
        return setSeconds(
            setMinutes(
                setHours(date, time.getHours()),
                time.getMinutes()
            ),
            time.getSeconds()
        )
    }

    // 处理时间变化（只更新内部状态，不触发回调）
    const handleTimeChange = (type: 'start' | 'end', newTime: Date) => {
        if (type === 'start') {
            setStartTime(newTime)
            if (date?.from) {
                const updatedRange = {
                    ...date,
                    from: combineDateTime(date.from, newTime)
                }
                setDate(updatedRange)
                // 注意：这里不调用 onDateChange，只有确认时才调用
            }
        } else {
            setEndTime(newTime)
            if (date?.to) {
                const updatedRange = {
                    ...date,
                    to: combineDateTime(date.to, newTime)
                }
                setDate(updatedRange)
                // 注意：这里不调用 onDateChange，只有确认时才调用
            }
        }
    }

    const formatDateRange = (range: DateRange | undefined) => {
        if (!range?.from) return "选择日期范围"

        // 根据屏幕尺寸选择不同的日期格式
        const getDateFormat = () => {
            if (isMobile) {
                // 移动端使用简洁格式
                return enableTime ? `MM/dd ${timeFormat}` : "MM/dd"
            } else if (isTablet) {
                // 平板端使用中等格式
                return enableTime ? `yyyy/MM/dd ${timeFormat}` : "yyyy/MM/dd"
            } else {
                // 桌面端使用完整格式
                return enableTime ? `yyyy年MM月dd日 ${timeFormat}` : "yyyy年MM月dd日"
            }
        }

        const dateFormat = getDateFormat()

        if (!range.to) {
            return format(range.from, dateFormat, { locale: zhCN })
        }

        // 如果是同一天且不启用时间，只显示一个日期
        if (range.from.toDateString() === range.to.toDateString() && !enableTime) {
            return format(range.from, dateFormat, { locale: zhCN })
        }

        // 移动端使用更简洁的分隔符
        const separator = isMobile ? "~" : " - "

        return `${format(range.from, dateFormat, { locale: zhCN })}${separator}${format(range.to, dateFormat, { locale: zhCN })}`
    }
    return (
        <div className={cn("grid gap-2", className)}>
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        id="date"
                        variant="outline"
                        className={cn(
                            "w-full justify-start text-left font-normal",
                            isMobile ? "h-10 px-3 text-sm" : "h-11 px-4",
                            !date && "text-muted-foreground"
                        )}
                    >
                        <CalendarIcon className={cn(
                            "opacity-70",
                            isMobile ? "mr-2 h-3 w-3" : "mr-3 h-4 w-4"
                        )} />
                        <span className={cn(
                            "flex-1 truncate",
                            isMobile ? "text-sm" : "text-base"
                        )}>
                            {formatDateRange(date)}
                        </span>
                        <ChevronDown className={cn(
                            "opacity-50",
                            isMobile ? "ml-1 h-3 w-3" : "ml-2 h-4 w-4"
                        )} />
                    </Button>
                </PopoverTrigger>
                <PopoverContent
                    className={cn(
                        "p-0",
                        isMobile ? "w-[95vw] max-w-sm" : "w-auto"
                    )}
                    align={isMobile ? "center" : "start"}
                    side={isMobile ? "bottom" : "bottom"}
                    sideOffset={isMobile ? 8 : 4}
                >
                    <div className={cn(
                        isMobile ? "flex flex-col" : "flex"
                    )}>
                        {/* 快捷选项侧边栏 */}
                        <Card className={cn(
                            "border-r rounded-r-none",
                            isMobile ? "w-full p-2 border-r-0 border-b rounded-b-none rounded-r-lg" : "w-48 p-3"
                        )}>
                            <div className={cn(
                                isMobile ? "space-y-0.5" : "space-y-1"
                            )}>
                                <h4 className={cn(
                                    "font-medium text-muted-foreground",
                                    isMobile ? "text-xs mb-2" : "text-sm mb-3"
                                )}>
                                    快捷选择
                                </h4>
                                <div className={cn(
                                    isMobile ? "grid grid-cols-2 gap-1" : "space-y-1"
                                )}>
                                    {quickOptions.map((option) => (
                                        <Button
                                            key={option.label}
                                            variant="ghost"
                                            size="sm"
                                            className={cn(
                                                "justify-start font-normal hover:bg-accent",
                                                isMobile ? "h-7 px-2 text-xs" : "w-full h-8 px-2 text-sm"
                                            )}
                                            onClick={() => handleQuickSelect(option.getValue())}
                                        >
                                            {option.label}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </Card>

                        {!isMobile && <Separator orientation="vertical" className="h-auto" />}
                        {/* 日历区域 */}
                        <div className={cn(
                            isMobile ? "p-2" : "p-3"
                        )}>
                            <Calendar
                                autoFocus
                                mode="range"
                                defaultMonth={date?.from}
                                selected={date}
                                onSelect={handleDateChange}
                                numberOfMonths={isMobile ? 1 : 2}
                                className="rounded-md"
                                locale={zhCN}
                                classNames={{
                                    months: cn(
                                        "flex space-y-4",
                                        isMobile ? "flex-col" : "flex-col sm:flex-row sm:space-x-4 sm:space-y-0"
                                    ),
                                    month: cn(
                                        isMobile ? "space-y-2" : "space-y-4"
                                    ),
                                    caption: "flex justify-center pt-1 relative items-center",
                                    caption_label: cn(
                                        "font-medium",
                                        isMobile ? "text-sm" : "text-sm"
                                    ),
                                    nav: "space-x-1 flex items-center",
                                    nav_button: cn(
                                        "bg-transparent p-0 opacity-50 hover:opacity-100",
                                        isMobile ? "h-6 w-6" : "h-7 w-7"
                                    ),
                                    nav_button_previous: "absolute left-1",
                                    nav_button_next: "absolute right-1",
                                    table: cn(
                                        "w-full border-collapse",
                                        isMobile ? "space-y-0.5" : "space-y-1"
                                    ),
                                    head_row: "flex",
                                    head_cell: cn(
                                        "text-muted-foreground rounded-md font-normal",
                                        isMobile ? "w-7 text-xs" : "w-8 text-[0.8rem]"
                                    ),
                                    row: cn(
                                        "flex w-full",
                                        isMobile ? "mt-1" : "mt-2"
                                    ),
                                    cell: cn(
                                        "relative p-0 text-center focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",
                                        "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
                                        isMobile ? "text-xs" : "text-sm"
                                    ),
                                    day: cn(
                                        "p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
                                        isMobile ? "h-7 w-7 text-xs" : "h-8 w-8 text-sm"
                                    ),
                                    day_range_start: "day-range-start",
                                    day_range_end: "day-range-end",
                                    day_selected:
                                        "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                                    day_today: "bg-accent text-accent-foreground font-semibold",
                                    day_outside:
                                        "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
                                    day_disabled: "text-muted-foreground opacity-50",
                                    day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                                    day_hidden: "invisible",
                                }}
                            />
                            {/* 时间选择区域 */}
                            {enableTime && (
                                <>
                                    <Separator className={cn(
                                        isMobile ? "my-3" : "my-4"
                                    )} />
                                    <div className={cn(
                                        isMobile ? "space-y-3" : "space-y-4"
                                    )}>
                                        <div className={cn(
                                            "grid gap-3",
                                            isMobile ? "grid-cols-1" : "grid-cols-2"
                                        )}>
                                            {/* 开始时间 */}
                                            <div className={cn(
                                                isMobile ? "space-y-1.5" : "space-y-2"
                                            )}>
                                                <Label className={cn(
                                                    "font-medium",
                                                    isMobile ? "text-xs" : "text-sm"
                                                )}>
                                                    开始时间
                                                </Label>
                                                <TimePicker
                                                    value={startTime}
                                                    onChange={(time) => handleTimeChange('start', time)}
                                                    disabled={!date?.from}
                                                />
                                            </div>
                                            {/* 结束时间 */}
                                            <div className={cn(
                                                isMobile ? "space-y-1.5" : "space-y-2"
                                            )}>
                                                <Label className={cn(
                                                    "font-medium",
                                                    isMobile ? "text-xs" : "text-sm"
                                                )}>
                                                    结束时间
                                                </Label>
                                                <TimePicker
                                                    value={endTime}
                                                    onChange={(time) => handleTimeChange('end', time)}
                                                    disabled={!date?.to}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </>
                            )}
                            <Separator className={cn(
                                isMobile ? "my-3" : "my-4"
                            )} />
                            <div className={cn(
                                "flex items-center",
                                isMobile ? "justify-center gap-3" : "justify-between"
                            )}>
                                <Button
                                    variant="outline"
                                    size={isMobile ? "sm" : "sm"}
                                    onClick={() => {
                                        handleDateChange(undefined)
                                        setOpen(false)
                                    }}
                                    className={cn(
                                        isMobile ? "flex-1 text-xs" : ""
                                    )}
                                >
                                    清除
                                </Button>
                                <Button
                                    size={isMobile ? "sm" : "sm"}
                                    onClick={() => {
                                        if (date?.from && date?.to) {
                                            // 确认时才调用 onDateChange 回调
                                            onDateChange?.(date)
                                        }
                                        setOpen(false)
                                    }}
                                    disabled={!date?.from || !date?.to}
                                    className={cn(
                                        isMobile ? "flex-1 text-xs" : ""
                                    )}
                                >
                                    确认选择
                                </Button>
                            </div>
                        </div>
                    </div>
                </PopoverContent>
            </Popover>
        </div>
    )
}