import React, { useState } from 'react'
import { ChevronDown, ChevronRight, Loader2 } from 'lucide-react'
import { Badge } from './badge'
import { Checkbox } from './checkbox'
import { OrderService } from '@/services/financial.ts'
import { useBreakpoint } from '@/hooks/useBreakpoint'
import { FINANCIAL_RESPONSIVE_CONFIG, TABLE_COLUMN_CONFIG } from '@/constants/responsive'
import { cn } from '@/utils'
import type { OrderVO } from '@/types'

interface ExpandableTableProps {
  orders: OrderVO[]
  selectedOrderIds: string[]
  onCheckboxChange: (payid: string, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  getStatusBadgeVariant: (status: number) => "success" | "default" | "warning" | "info" | "destructive" | "outline" | "secondary"
  onViewDetail: (order: OrderVO) => void
  subOrdersMap: Map<string, OrderVO[]>
  onSubOrdersMapChange: (map: Map<string, OrderVO[]>) => void
  getOrderSelectionState: (orderId: string) => { selected: boolean; indeterminate: boolean }
}

export const ExpandableTable: React.FC<ExpandableTableProps> = ({
  orders,
  selectedOrderIds,
  onCheckboxChange,
  onSelectAll,
  getStatusBadgeVariant,
  onViewDetail,
  subOrdersMap,
  onSubOrdersMapChange,
  getOrderSelectionState
}) => {
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set())
  const [loadingSubOrders, setLoadingSubOrders] = useState<Set<string>>(new Set())
  const { isMobile, isTablet, current } = useBreakpoint()

  // 现在orders只包含主订单
  const mainOrders = orders

  // 获取当前断点下应该显示的列
  const getVisibleColumns = () => {
    const config = TABLE_COLUMN_CONFIG.orderManagement
    const visibleColumns: Record<string, boolean> = {}

    Object.entries(config).forEach(([columnKey, breakpoints]) => {
      visibleColumns[columnKey] = breakpoints.includes(current)
    })

    return visibleColumns
  }

  const visibleColumns = getVisibleColumns()
  /**
   * 计算主订单的总佣金（包括所有子订单）
   * 注意：后端已经使用OperationsService统一计算佣金，前端不再手动累加
   */
  const calculateTotalCommission = (mainOrder: OrderVO): number => {
    if (!mainOrder.isMainOrder) {
      return mainOrder.feeActual
    }

    // 对于主订单，直接使用后端计算的佣金值
    // 后端已经通过 OperationsService.getOperationsStats() 获取总充值并计算佣金
    return mainOrder.feeActual
  }

  /**
   * 获取主订单的总充值金额
   * 注意：主订单的amount_fans字段已经包含了OperationsMapper返回的准确总充值金额
   */
  const getTotalAmount = (mainOrder: OrderVO): number => {
    if (!mainOrder.isMainOrder) {
      return mainOrder.totalAmount
    }

    // 对于主订单，直接使用amount_fans字段
    // 后端已经将OperationsMapper.getSubordinateProfit()的zongchongzhi赋值给amount_fans
    return mainOrder.amountFans
  }

  const toggleExpand = async (payid: string) => {
    const newExpanded = new Set(expandedOrders)

    if (newExpanded.has(payid)) {
      // 收起
      newExpanded.delete(payid)
    } else {
      // 展开 - 需要加载子订单
      newExpanded.add(payid)

      // 如果还没有加载过子订单，则加载
      if (!subOrdersMap.has(payid)) {
        setLoadingSubOrders(prev => new Set(prev).add(payid))

        try {
          const subOrders = await OrderService.getSubOrders(payid)
          const newMap = new Map(subOrdersMap).set(payid, subOrders)
          onSubOrdersMapChange(newMap)
        } catch (error) {
          console.error('加载子订单失败:', error)
          // 加载失败时收起
          newExpanded.delete(payid)
        } finally {
          setLoadingSubOrders(prev => {
            const newSet = new Set(prev)
            newSet.delete(payid)
            return newSet
          })
        }
      }
    }

    setExpandedOrders(newExpanded)
  }

  const renderOrderRow = (order: OrderVO, isSubOrder = false, level = 0) => {
    const hasSubOrders = !isSubOrder && order.isMainOrder // 主订单都可能有子订单
    const isExpanded = expandedOrders.has(order.payid)
    const isLoading = loadingSubOrders.has(order.payid)
    const indentClass = level > 0 ? `pl-${4 + level * 4}` : ''
    const bgClass = isSubOrder ? 'bg-gray-50' : 'bg-white hover:bg-gray-50'

    return (
      <tr key={order.payid} className={bgClass}>
        <td className={cn(
          FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
          indentClass
        )}>
          <div className={cn(
            "flex items-center",
            isMobile ? "gap-1" : "gap-2"
          )}>
            {hasSubOrders ? (
              <button
                onClick={() => toggleExpand(order.payid)}
                className={cn(
                  "p-1 hover:bg-gray-200 rounded transition-colors",
                  isMobile ? "p-0.5" : "p-1"
                )}
                aria-label={isExpanded ? "收起" : "展开"}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className={cn(
                    "text-gray-600 animate-spin",
                    isMobile ? "w-3 h-3" : "w-4 h-4"
                  )} />
                ) : isExpanded ? (
                  <ChevronDown className={cn(
                    "text-gray-600",
                    isMobile ? "w-3 h-3" : "w-4 h-4"
                  )} />
                ) : (
                  <ChevronRight className={cn(
                    "text-gray-600",
                    isMobile ? "w-3 h-3" : "w-4 h-4"
                  )} />
                )}
              </button>
            ) : (
              <div className={cn(
                isMobile ? "w-4" : "w-6"
              )} /> // 占位符，保持对齐
            )}
            <Checkbox
              checked={(() => {
                const state = getOrderSelectionState(order.payid)
                return state.selected
              })()}
              indeterminate={(() => {
                const state = getOrderSelectionState(order.payid)
                return state.indeterminate
              })()}
              onCheckedChange={(checked) => onCheckboxChange(order.payid, checked === true)}
            />
          </div>
        </td>

        {/* 订单信息 */}
        <td className={cn(
          FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
        )}>
          <div className={cn(
            isMobile ? "space-y-0.5" : "space-y-1"
          )}>
            <div className={cn(
              "font-medium text-gray-900",
              FINANCIAL_RESPONSIVE_CONFIG.table.textSize
            )}>
              {isMobile ? order.payid.slice(-8) : order.payid}
            </div>
            {order.parentsPayid && !isMobile && (
              <div className={cn(
                "text-gray-500",
                FINANCIAL_RESPONSIVE_CONFIG.table.textSize
              )}>
                父订单: {order.parentsPayid.slice(-8)}
              </div>
            )}
            <div className={cn(
              "flex items-center",
              isMobile ? "gap-1" : "gap-2"
            )}>
              {order.isMainOrder ? (
                <Badge variant="default" className={cn(
                  isMobile && "text-xs px-1 py-0"
                )}>
                  {isMobile ? "主" : "主订单"}
                </Badge>
              ) : (
                <Badge variant="secondary" className={cn(
                  isMobile && "text-xs px-1 py-0"
                )}>
                  {isMobile ? "子" : "子订单"}
                </Badge>
              )}
              {hasSubOrders && subOrdersMap.has(order.payid) && !isMobile && (
                <Badge variant="outline">
                  {subOrdersMap.get(order.payid)?.length || 0}个子订单
                </Badge>
              )}
            </div>
          </div>
        </td>

        {/* 用户信息 - 中等屏幕以上显示 */}
        {visibleColumns.userName && (
          <td className={cn(
            FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
          )}>
            <div className={cn(
              isMobile ? "space-y-0.5" : "space-y-1"
            )}>
              {order.agent && (
                <div className={cn(
                  FINANCIAL_RESPONSIVE_CONFIG.table.textSize
                )}>
                  <span className="text-gray-500">代理:</span>
                  <span className="ml-1 font-medium">
                    {order.agentNickname || order.agent}
                  </span>
                </div>
              )}
              {order.anchor && (
                <div className={cn(
                  FINANCIAL_RESPONSIVE_CONFIG.table.textSize
                )}>
                  <span className="text-gray-500">主播:</span>
                  <span className="ml-1 font-medium">
                    {order.anchorNickname || order.anchor}
                  </span>
                </div>
              )}
              <div className={cn(
                "text-gray-500",
                FINANCIAL_RESPONSIVE_CONFIG.table.textSize
              )}>
                收款人: {order.feePerson}
              </div>
            </div>
          </td>
        )}

        {/* 金额信息 */}
        <td className={cn(
          "text-right",
          FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
        )}>
          <div className={cn(
            isMobile ? "space-y-0.5" : "space-y-1"
          )}>
            <div className={cn(
              "font-medium text-gray-900",
              FINANCIAL_RESPONSIVE_CONFIG.table.textSize
            )}>
              {order.isMainOrder ? (
                <>
                  {OrderService.formatAmount(getTotalAmount(order))}
                  {subOrdersMap.has(order.payid) && subOrdersMap.get(order.payid)!.length > 0 && !isMobile && (
                    <div className="text-xs text-blue-600">
                      (含{subOrdersMap.get(order.payid)!.length}个子订单)
                    </div>
                  )}
                </>
              ) : (
                OrderService.formatAmount(order.amountSelf + order.amountFans)
              )}
            </div>
            {!isMobile && (
              <div className={cn(
                "text-gray-500",
                FINANCIAL_RESPONSIVE_CONFIG.table.textSize
              )}>
                {order.isMainOrder ? (
                  <>
                    {order.actualSettlementAmount > 0 ? (
                      <>实际结算: {OrderService.formatAmount(order.actualSettlementAmount)}</>
                    ) : (
                      <>佣金合计: {OrderService.formatAmount(calculateTotalCommission(order))}</>
                    )}
                  </>
                ) : (
                  <>
                    {order.actualSettlementAmount > 0 ? (
                      <>实际结算: {OrderService.formatAmount(order.actualSettlementAmount)}</>
                    ) : (
                      <>佣金: {OrderService.formatAmount(order.feeActual)}</>
                    )}
                  </>
                )}
              </div>
            )}
            <div className="text-sm text-gray-500">
              比例: {order.feeRate}%
            </div>
          </div>
        </td>

        {/* 状态 */}
        <td className={cn(
          "text-center",
          FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
        )}>
          <div className={cn(
            isMobile ? "space-y-0.5" : "space-y-1"
          )}>
            <Badge
              variant={getStatusBadgeVariant(order.orderStatus)}
              className={cn(
                isMobile && "text-xs px-1 py-0"
              )}
            >
              {isMobile ? order.orderStatusDesc.slice(0, 2) : order.orderStatusDesc}
            </Badge>
            {!isMobile && (
              <div className={cn(
                "text-gray-500",
                FINANCIAL_RESPONSIVE_CONFIG.table.textSize
              )}>
                {order.settlementStatusDesc}
              </div>
            )}
          </div>
        </td>

        {/* 结算时间区间列 - 大屏幕显示 */}
        {visibleColumns.createTime && (
          <td className={cn(
            "text-center",
            FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
          )}>
            <div className="space-y-2">
              {/* 结算时间区间 - 主要信息 */}
              {order.settlementStartTime && order.settlementEndTime ? (
                <div className="space-y-1">
                  <div className={cn(
                    "text-gray-900 font-medium",
                    FINANCIAL_RESPONSIVE_CONFIG.table.textSize
                  )}>
                    {OrderService.formatTimestamp(order.settlementStartTime)}
                  </div>
                  <div className="text-xs text-gray-500">至</div>
                  <div className={cn(
                    "text-gray-900 font-medium",
                    FINANCIAL_RESPONSIVE_CONFIG.table.textSize
                  )}>
                    {OrderService.formatTimestamp(order.settlementEndTime)}
                  </div>
                </div>
              ) : (
                <div className="space-y-1">
                  <div className={cn(
                    "font-semibold text-gray-500",
                    FINANCIAL_RESPONSIVE_CONFIG.table.textSize
                  )}>
                    结算时间区间
                  </div>
                  <div className="text-xs text-gray-400">
                    未设置结算时间
                  </div>
                </div>
              )}
            </div>
          </td>
        )}

        {/* 创建时间列 - 大屏幕显示 */}
        {visibleColumns.createTime && (
          <td className={cn(
            "text-center",
            FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
          )}>
            {/* 创建时间 - 次要信息 */}
            <div className="pt-2 border-gray-100">
              <div className={cn(
                "text-gray-600",
                FINANCIAL_RESPONSIVE_CONFIG.table.textSize
              )}>
                {OrderService.formatTimestamp(order.createTime)}
              </div>
            </div>
          </td>
        )}

        {/* 操作列 - 始终显示 */}
        <td className={cn(
          "text-center",
          FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding
        )}>
          <div className="flex items-center justify-center gap-2">
            <button
              onClick={() => onViewDetail(order)}
              className={cn(
                "text-blue-600 hover:text-blue-800",
                FINANCIAL_RESPONSIVE_CONFIG.table.actionButtonSize
              )}
            >
              {isMobile ? "详情" : "查看"}
            </button>
          </div>
        </td>
      </tr>
    )
  }

  const renderExpandableRows = () => {
    const rows: React.ReactElement[] = []
    
    mainOrders.forEach(mainOrder => {
      // 渲染主订单
      rows.push(renderOrderRow(mainOrder))
      
      // 如果展开且有子订单，渲染子订单
      if (expandedOrders.has(mainOrder.payid) && subOrdersMap.has(mainOrder.payid)) {
        const subOrders = subOrdersMap.get(mainOrder.payid)!
        subOrders.forEach(subOrder => {
          rows.push(renderOrderRow(subOrder, true, 1))
        })
      }
    })
    
    return rows
  }

  return (
    <div className={cn(
      FINANCIAL_RESPONSIVE_CONFIG.table.container
    )}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {/* 复选框列 - 始终显示 */}
            <th className={cn(
              "text-left font-medium text-gray-500 uppercase tracking-wider",
              FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
              FINANCIAL_RESPONSIVE_CONFIG.table.headerText
            )}>
              <Checkbox
                checked={(() => {
                  if (orders.length === 0) return false

                  // 计算所有可选择的订单数量（包括子订单）
                  let totalSelectableOrders = orders.length
                  subOrdersMap.forEach((subOrders) => {
                    totalSelectableOrders += subOrders.length
                  })

                  return selectedOrderIds.length === totalSelectableOrders
                })()}
                indeterminate={(() => {
                  if (orders.length === 0 || selectedOrderIds.length === 0) return false

                  // 计算所有可选择的订单数量（包括子订单）
                  let totalSelectableOrders = orders.length
                  subOrdersMap.forEach((subOrders) => {
                    totalSelectableOrders += subOrders.length
                  })

                  return selectedOrderIds.length > 0 && selectedOrderIds.length < totalSelectableOrders
                })()}
                onCheckedChange={(checked) => onSelectAll(checked === true)}
              />
            </th>

            {/* 订单信息列 - 始终显示 */}
            <th className={cn(
              "text-left font-medium text-gray-500 uppercase tracking-wider",
              FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
              FINANCIAL_RESPONSIVE_CONFIG.table.headerText
            )}>
              订单信息
            </th>

            {/* 用户信息列 - 中等屏幕以上显示 */}
            {visibleColumns.userName && (
              <th className={cn(
                "text-left font-medium text-gray-500 uppercase tracking-wider",
                FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
                FINANCIAL_RESPONSIVE_CONFIG.table.headerText
              )}>
                用户信息
              </th>
            )}

            {/* 金额信息列 - 始终显示 */}
            <th className={cn(
              "text-right font-medium text-gray-500 uppercase tracking-wider",
              FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
              FINANCIAL_RESPONSIVE_CONFIG.table.headerText
            )}>
              金额信息
            </th>

            {/* 状态列 - 始终显示 */}
            <th className={cn(
              "text-center font-medium text-gray-500 uppercase tracking-wider",
              FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
              FINANCIAL_RESPONSIVE_CONFIG.table.headerText
            )}>
              状态
            </th>

            {/* 结算时间区间列 - 大屏幕显示 */}
            {visibleColumns.createTime && (
              <th className={cn(
                "text-center font-medium text-gray-500 uppercase tracking-wider",
                FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
                FINANCIAL_RESPONSIVE_CONFIG.table.headerText
              )}>
                结算时间区间
              </th>
            )}

            {/* 创建时间列 - 大屏幕显示 */}
            {visibleColumns.createTime && (
              <th className={cn(
                "text-center font-medium text-gray-500 uppercase tracking-wider",
                FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
                FINANCIAL_RESPONSIVE_CONFIG.table.headerText
              )}>
                创建时间
              </th>
            )}

            {/* 操作列 - 始终显示 */}
            <th className={cn(
              "text-center font-medium text-gray-500 uppercase tracking-wider",
              FINANCIAL_RESPONSIVE_CONFIG.table.cellPadding,
              FINANCIAL_RESPONSIVE_CONFIG.table.headerText
            )}>
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {renderExpandableRows()}
        </tbody>
      </table>
    </div>
  )
}
