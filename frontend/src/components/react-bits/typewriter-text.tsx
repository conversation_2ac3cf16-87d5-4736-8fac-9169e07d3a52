import React, { useState, useEffect } from 'react';
import { cn } from '../../lib/utils';

interface TypewriterTextProps {
  text: string | string[];
  speed?: number;
  delay?: number;
  loop?: boolean;
  cursor?: boolean;
  className?: string;
  onComplete?: () => void;
}

const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 100,
  delay = 1000,
  loop = false,
  cursor = true,
  className,
  onComplete,
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showCursor, setShowCursor] = useState(true);

  const textArray = Array.isArray(text) ? text : [text];
  const currentText = textArray[currentTextIndex];

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (currentIndex < currentText.length) {
          setDisplayText(currentText.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        } else {
          // Finished typing current text
          if (textArray.length > 1 && loop) {
            setTimeout(() => setIsDeleting(true), delay);
          } else if (onComplete) {
            onComplete();
          }
        }
      } else {
        // Deleting
        if (currentIndex > 0) {
          setDisplayText(currentText.slice(0, currentIndex - 1));
          setCurrentIndex(currentIndex - 1);
        } else {
          // Finished deleting
          setIsDeleting(false);
          setCurrentTextIndex((currentTextIndex + 1) % textArray.length);
        }
      }
    }, isDeleting ? speed / 2 : speed);

    return () => clearTimeout(timeout);
  }, [currentIndex, currentText, isDeleting, speed, delay, textArray, currentTextIndex, loop, onComplete]);

  // Cursor blinking effect
  useEffect(() => {
    if (cursor) {
      const cursorInterval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);
      return () => clearInterval(cursorInterval);
    }
    return undefined;
  }, [cursor]);

  return (
    <span className={cn('inline-block', className)}>
      {displayText}
      {cursor && (
        <span
          className={cn(
            'inline-block w-0.5 h-5 bg-current ml-1 transition-opacity duration-100',
            showCursor ? 'opacity-100' : 'opacity-0'
          )}
        />
      )}
    </span>
  );
};

export { TypewriterText };
export type { TypewriterTextProps };