/**
 * 消息工具测试页面
 * 用于测试替换后的弹层组件功能
 */

import React from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { success, error, warning, info, confirm, confirmDelete } from '@/utils'

export const MessageTest: React.FC = () => {
  
  /**
   * 测试成功消息
   */
  const testSuccess = () => {
    success('这是一个成功消息示例')
  }

  /**
   * 测试错误消息
   */
  const testError = () => {
    error('这是一个错误消息示例')
  }

  /**
   * 测试警告消息
   */
  const testWarning = () => {
    warning('这是一个警告消息示例')
  }

  /**
   * 测试信息消息
   */
  const testInfo = () => {
    info('这是一个信息消息示例')
  }

  /**
   * 测试确认对话框
   */
  const testConfirm = async () => {
    const result = await confirm('您确定要执行此操作吗？', '确认操作')
    if (result) {
      success('您选择了确认')
    } else {
      info('您选择了取消')
    }
  }

  /**
   * 测试删除确认对话框
   */
  const testConfirmDelete = async () => {
    const result = await confirmDelete('此操作将永久删除数据，无法恢复。', '确认删除')
    if (result) {
      success('删除操作已确认')
    } else {
      info('删除操作已取消')
    }
  }

  /**
   * 测试批量结算场景
   */
  const testBatchSettle = async () => {
    // 模拟选择检查
    const hasSelection = Math.random() > 0.5
    if (!hasSelection) {
      warning('请选择要结算的订单')
      return
    }

    // 模拟操作
    try {
      // 模拟异步操作
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟成功或失败
      const isSuccess = Math.random() > 0.3
      if (isSuccess) {
        const count = Math.floor(Math.random() * 10) + 1
        success(`成功结算 ${count} 个订单`)
      } else {
        throw new Error('网络错误')
      }
    } catch (err) {
      error('批量结算失败')
    }
  }

  /**
   * 测试批量取消场景
   */
  const testBatchCancel = async () => {
    // 模拟选择检查
    const hasSelection = Math.random() > 0.5
    if (!hasSelection) {
      warning('请选择要取消的订单')
      return
    }

    // 确认对话框
    const confirmed = await confirm(
      '确定要取消选中的订单吗？此操作不可撤销。',
      '确认取消订单',
      { variant: 'destructive', confirmText: '取消订单' }
    )
    
    if (!confirmed) {
      return
    }

    // 模拟操作
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const isSuccess = Math.random() > 0.3
      if (isSuccess) {
        const count = Math.floor(Math.random() * 10) + 1
        success(`成功取消 ${count} 个订单`)
      } else {
        throw new Error('网络错误')
      }
    } catch (err) {
      error('批量取消失败')
    }
  }

  /**
   * 测试导出Excel场景
   */
  const testExportExcel = async () => {
    // 模拟数据检查
    const hasData = Math.random() > 0.3
    if (!hasData) {
      warning('当前没有数据可导出，请先查询订单数据')
      return
    }

    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const isSuccess = Math.random() > 0.2
      if (isSuccess) {
        const fileName = `代理结算_${new Date().toISOString().slice(0, 10)}.xlsx`
        const fileSize = (Math.random() * 500 + 100).toFixed(2)
        success(`Excel文件导出成功！文件名：${fileName}，文件大小：${fileSize} KB`)
      } else {
        throw new Error('服务器返回的文件为空')
      }
    } catch (err: any) {
      const errorMessage = err.message || '导出Excel失败，请重试'
      error(`导出失败：${errorMessage}`)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">消息工具测试页面</h1>
        <p className="text-gray-600 mt-1">测试替换后的弹层组件功能</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 基础消息测试 */}
        <Card>
          <CardHeader>
            <CardTitle>基础消息测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testSuccess} className="w-full" variant="default">
              测试成功消息
            </Button>
            <Button onClick={testError} className="w-full" variant="destructive">
              测试错误消息
            </Button>
            <Button onClick={testWarning} className="w-full" variant="outline">
              测试警告消息
            </Button>
            <Button onClick={testInfo} className="w-full" variant="secondary">
              测试信息消息
            </Button>
          </CardContent>
        </Card>

        {/* 确认对话框测试 */}
        <Card>
          <CardHeader>
            <CardTitle>确认对话框测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testConfirm} className="w-full" variant="default">
              测试确认对话框
            </Button>
            <Button onClick={testConfirmDelete} className="w-full" variant="destructive">
              测试删除确认
            </Button>
          </CardContent>
        </Card>

        {/* 订单场景测试 */}
        <Card>
          <CardHeader>
            <CardTitle>订单场景测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testBatchSettle} className="w-full" variant="default">
              测试批量结算
            </Button>
            <Button onClick={testBatchCancel} className="w-full" variant="outline">
              测试批量取消
            </Button>
            <Button onClick={testExportExcel} className="w-full" variant="secondary">
              测试导出Excel
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• <strong>基础消息测试</strong>：测试不同类型的 Toast 消息显示</p>
            <p>• <strong>确认对话框测试</strong>：测试替代原生 confirm() 的对话框</p>
            <p>• <strong>订单场景测试</strong>：模拟真实的订单操作场景，包含随机的成功/失败结果</p>
            <p>• 所有测试都使用了统一的 UI 组件，替代了原生的 alert() 和 confirm()</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default MessageTest
