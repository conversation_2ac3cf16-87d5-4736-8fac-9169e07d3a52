import React, { useState, useCallback, useMemo } from 'react'
import { RefreshCw, BarChart3, <PERSON><PERSON>dingUp, Settings, Eye, EyeOff } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import { usePageTitle } from '@/router/guards'
import { useToast } from '@/hooks/useToast'
import { cn } from '@/utils'
import { formatTimeFor<PERSON><PERSON>, getPresetTimeRange } from '@/utils/financial'
import { TimeRangePreset } from '@/types/financial'
import { FINANCIAL_RESPONSIVE_CONFIG } from '@/constants/responsive'
import { useBreakpoint } from '@/hooks/useBreakpoint'
import type { DateRange } from "react-day-picker"
import type { FinancialStatsRequest } from '@/types/financial'
import DateRangePicker from "@/components/ui/date-range-picker"

// 导入图表组件
import {
  RevenueMultiLineChart,
  UserActivityHeatmap,
  BoxConsumptionArea,
  UserBehaviorScatter
} from '@/components/charts'

// 导入图表数据服务
import { ChartDataService } from '@/services/chartData'

/**
 * 图表配置接口
 */
interface ChartConfig {
  id: string
  name: string
  description: string
  category: 'core' | 'business' | 'user'
  enabled: boolean
  height: number
}

/**
 * 默认图表配置
 */
const DEFAULT_CHART_CONFIGS: ChartConfig[] = [
  {
    id: 'revenue-trend',
    name: '业务收入趋势分析',
    description: '9种业务类型收入趋势及佣金支出分析',
    category: 'core',
    enabled: true,
    height: 400
  },

  {
    id: 'user-activity',
    name: '用户活跃度热力图',
    description: '用户活跃时间分布分析',
    category: 'core',
    enabled: true,
    height: 400
  },

  {
    id: 'box-consumption',
    name: '开箱消费趋势',
    description: '用户开箱行为和消费趋势分析',
    category: 'business',
    enabled: true,
    height: 400
  },

  {
    id: 'user-behavior',
    name: '用户行为分析',
    description: '用户充值频次与金额关系分析',
    category: 'business',
    enabled: true,
    height: 400
  },


]

/**
 * 财务数据图表页面组件
 */
const FinancialChartsPage: React.FC = () => {
  // 设置页面标题
  usePageTitle('财务数据图表')

  const { toast } = useToast()
  const { isMobile, isTablet } = useBreakpoint()

  // 状态管理
  const [, setSelectedRange] = useState<DateRange | undefined>()
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>(DEFAULT_CHART_CONFIGS)
  const [loading, setLoading] = useState(false)
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 查询参数
  const [currentQueryParams, setCurrentQueryParams] = useState<FinancialStatsRequest>(() => ({
    ...getPresetTimeRange(TimeRangePreset.THIS_WEEK),
    includeAnchor: true
  }))

  // 图表数据状态
  const [chartData, setChartData] = useState<{
    revenueTrend: any[]
    userActivity: any[]
    boxConsumption: any[]
    userBehavior: any[]
  }>({
    revenueTrend: [],
    userActivity: [],
    boxConsumption: [],
    userBehavior: []
  })

  // 处理日期范围确认选择
  const handleDateRangeConfirm = useCallback((dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange?.to) {
      const startTime = formatTimeForApi(dateRange.from.toISOString())
      const endTime = formatTimeForApi(dateRange.to.toISOString())

      const queryParams: FinancialStatsRequest = {
        startTime,
        endTime,
        includeAnchor: true
      }

      setCurrentQueryParams(queryParams)
      setSelectedRange(dateRange)
      
      toast({
        title: '查询已更新',
        description: '正在获取新的图表数据...'
      })
    } else if (dateRange === undefined) {
      // 清除选择，使用默认范围
      const defaultParams = {
        ...getPresetTimeRange(TimeRangePreset.THIS_WEEK),
        includeAnchor: true
      }
      setCurrentQueryParams(defaultParams)
      setSelectedRange(undefined)
    }
  }, [toast])

  // 加载图表数据
  const loadChartData = useCallback(async () => {
    setLoading(true)
    try {
      const [
        revenueTrend,
        userActivity,
        boxConsumption,
        userBehavior
      ] = await Promise.all([
        ChartDataService.getRevenueTrendData(currentQueryParams),
        ChartDataService.getUserActivityHeatmapData(currentQueryParams),
        ChartDataService.getBoxConsumptionData(currentQueryParams),
        ChartDataService.getUserBehaviorScatterData(currentQueryParams)
      ])

      setChartData({
        revenueTrend,
        userActivity,
        boxConsumption,
        userBehavior
      })

      toast({
        title: '数据加载成功',
        description: '所有图表数据已更新',
        duration: 2000
      })
    } catch (error) {
      console.error('加载图表数据失败:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setError(errorMessage)
      toast({
        title: '数据加载失败',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [currentQueryParams, toast])

  // 初始加载和参数变化时重新加载
  React.useEffect(() => {
    loadChartData()
  }, [loadChartData])

  // 自动刷新
  React.useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      loadChartData()
    }, 60000) // 1分钟刷新一次

    return () => clearInterval(interval)
  }, [autoRefresh, loadChartData])

  // 切换图表显示状态
  const toggleChartEnabled = useCallback((chartId: string) => {
    setChartConfigs(prev => prev.map(config =>
      config.id === chartId
        ? { ...config, enabled: !config.enabled }
        : config
    ))
  }, [])

  // 更新图表高度
  const updateChartHeight = useCallback((chartId: string, height: number) => {
    setChartConfigs(prev => prev.map(config =>
      config.id === chartId
        ? { ...config, height }
        : config
    ))
  }, [])

  // 重置图表配置
  const resetChartConfigs = useCallback(() => {
    setChartConfigs(DEFAULT_CHART_CONFIGS)
  }, [])

  // 获取启用的图表配置（移除分类过滤，显示所有启用的图表）
  const enabledCharts = useMemo(() => {
    return chartConfigs.filter(config => config.enabled)
  }, [chartConfigs])

  // 渲染图表组件
  const renderChart = useCallback((config: ChartConfig) => {
    if (!config.enabled) return null

    const commonProps = {
      loading,
      showRefresh: false, // 移除单个图表的刷新按钮，使用全局刷新
      height: config.height
    }

    switch (config.id) {
      case 'revenue-trend':
        return (
          <RevenueMultiLineChart
            key={config.id}
            data={chartData.revenueTrend}
            {...commonProps}
          />
        )

      case 'user-activity':
        return (
          <UserActivityHeatmap
            key={config.id}
            data={chartData.userActivity}
            {...commonProps}
          />
        )

      case 'box-consumption':
        return (
          <BoxConsumptionArea
            key={config.id}
            data={chartData.boxConsumption}
            {...commonProps}
          />
        )

      case 'user-behavior':
        return (
          <UserBehaviorScatter
            key={config.id}
            data={chartData.userBehavior}
            {...commonProps}
          />
        )


      default:
        return null
    }
  }, [chartData, loading, loadChartData])

  return (
    <div className={cn(
      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionSpacing,
      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.containerPadding
    )}>
      {/* 页面标题和操作区域 */}
      <div className={cn(
        "flex items-start justify-between",
        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.controlsLayout
      )}>
        <div className="flex-1 min-w-0">
          <h1 className={cn(
            "font-bold tracking-tight flex items-center",
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.titleSize
          )}>
            <BarChart3 className={cn(
              "mr-2 sm:mr-3 text-primary",
              isMobile ? "w-5 h-5" : "w-6 h-6 sm:w-8 sm:h-8"
            )} />
            财务数据图表
          </h1>
          <p className={cn(
            "text-muted-foreground mt-1",
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
          )}>
            {isMobile ? "可视化财务数据分析" : "通过可视化图表深度分析平台财务数据和用户行为"}
          </p>
        </div>

        {/* 操作按钮组 */}
        <div className={cn(
          "flex items-center",
          isMobile ? "flex-col space-y-2" : "space-x-3"
        )}>
          {/* 自动刷新开关 */}
          <div className={cn(
            "flex items-center",
            isMobile ? "space-x-1" : "space-x-2"
          )}>
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={setAutoRefresh}
              disabled={loading}
            />
            <Label
              htmlFor="auto-refresh"
              className={cn(
                FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
              )}
            >
              {isMobile ? "自动" : "自动刷新"}
            </Label>
          </div>

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size={isMobile ? "sm" : "sm"}
            onClick={loadChartData}
            disabled={loading}
            className={cn(
              "flex items-center",
              isMobile ? "space-x-1" : "space-x-2"
            )}
          >
            <RefreshCw className={cn(
              loading && "animate-spin",
              isMobile ? "h-3 w-3" : "h-4 w-4"
            )} />
            {!isMobile && <span>刷新数据</span>}
          </Button>

          {/* 设置按钮 */}
          <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "flex items-center",
                  isMobile ? "space-x-1" : "space-x-2"
                )}
              >
                <Settings className={cn(
                  isMobile ? "h-3 w-3" : "h-4 w-4"
                )} />
                {!isMobile && <span>图表设置</span>}
              </Button>
            </DialogTrigger>
            <DialogContent className={cn(
              "max-h-[80vh] overflow-y-auto",
              isMobile ? "max-w-[95vw] mx-2" : "max-w-4xl"
            )}>
              <DialogHeader>
                <DialogTitle>图表设置</DialogTitle>
                <DialogDescription>
                  自定义图表显示和配置选项
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                {/* 全局操作 */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">全局操作</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetChartConfigs}
                  >
                    重置所有配置
                  </Button>
                </div>

                <Separator />

                {/* 按分类显示图表配置 */}
                <Tabs defaultValue="core" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="core">核心业务</TabsTrigger>
                    <TabsTrigger value="business">业务分析</TabsTrigger>
                  </TabsList>

                  {(['core', 'business', 'user'] as const).map(category => (
                    <TabsContent key={category} value={category} className="space-y-4">
                      {chartConfigs
                        .filter(config => config.category === category)
                        .map(config => (
                          <Card key={config.id} className="p-4">
                            <div className="space-y-4">
                              {/* 图表基本信息 */}
                              <div className="flex items-center justify-between">
                                <div>
                                  <h4 className="font-medium">{config.name}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {config.description}
                                  </p>
                                </div>
                                <Switch
                                  checked={config.enabled}
                                  onCheckedChange={() => toggleChartEnabled(config.id)}
                                />
                              </div>

                              {/* 图表高度设置 */}
                              {config.enabled && (
                                <div className="space-y-2">
                                  <Label className="text-sm">
                                    图表高度: {config.height}px
                                  </Label>
                                  <Slider
                                    value={[config.height]}
                                    onValueChange={([value]) => updateChartHeight(config.id, value)}
                                    min={200}
                                    max={800}
                                    step={50}
                                    className="w-full"
                                  />
                                  <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>200px</span>
                                    <span>800px</span>
                                  </div>
                                </div>
                              )}
                            </div>
                          </Card>
                        ))}
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">查询条件</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 时间范围选择器 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">时间范围</Label>
              <DateRangePicker
                enableTime={true}
                timeFormat="HH:mm:ss"
                onDateChange={handleDateRangeConfirm}
                className="max-w-md"
              />
            </div>

            {/* 图表控制 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">图表显示控制</Label>
              <div className="flex flex-wrap gap-2">
                {chartConfigs.map(config => (
                  <Badge
                    key={config.id}
                    variant={config.enabled ? "default" : "secondary"}
                    className="cursor-pointer flex items-center gap-1"
                    onClick={() => toggleChartEnabled(config.id)}
                  >
                    {config.enabled ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                    {config.name}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-destructive bg-destructive/10">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 rounded-full bg-destructive" />
              <div>
                <h3 className="font-medium text-destructive">数据加载失败</h3>
                <p className="text-sm text-muted-foreground mt-1">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    setError(null)
                    loadChartData()
                  }}
                >
                  重试
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 图表展示区域 - 单页面垂直布局 */}
      <div className={cn(
        isMobile ? "space-y-4" : "space-y-6 lg:space-y-8"
      )}>
        {/* 核心业务图表 */}
        <div className={cn(
          isMobile ? "space-y-3" : "space-y-4 lg:space-y-6"
        )}>
          <div className={cn(
            "flex items-center",
            isMobile ? "gap-2" : "gap-3"
          )}>
            <TrendingUp className={cn(
              "text-primary",
              isMobile ? "h-4 w-4" : "h-5 w-5 lg:h-6 lg:w-6"
            )} />
            <h2 className={cn(
              "font-semibold",
              FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionTitle
            )}>
              核心业务分析
            </h2>
          </div>
          <div className={cn(
            "grid gap-4",
            FINANCIAL_RESPONSIVE_CONFIG.charts.gridLayout,
            FINANCIAL_RESPONSIVE_CONFIG.charts.gap
          )}>
            {enabledCharts
              .filter(config => config.category === 'core')
              .map(config => (
                <Card key={config.id} className="overflow-hidden">
                  <CardHeader className={cn(
                    isMobile ? "pb-2" : "pb-4"
                  )}>
                    <CardTitle className={cn(
                      "flex items-center justify-between",
                      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                    )}>
                      <span>{config.name}</span>
                      <Badge
                        variant="secondary"
                        className={cn(
                          isMobile ? "text-xs px-1 py-0" : "text-xs"
                        )}
                      >
                        {config.category === 'core' ? '核心' :
                         config.category === 'business' ? '业务' : '用户'}
                      </Badge>
                    </CardTitle>
                    {!isMobile && (
                      <p className={cn(
                        "text-muted-foreground",
                        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                      )}>
                        {config.description}
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className={cn(
                    isMobile ? "pt-0 px-3 pb-3" : "pt-0"
                  )}>
                    {renderChart(config)}
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>

        {/* 业务分析图表 */}
        <div className={cn(
          isMobile ? "space-y-3" : "space-y-4 lg:space-y-6"
        )}>
          <div className={cn(
            "flex items-center",
            isMobile ? "gap-2" : "gap-3"
          )}>
            <BarChart3 className={cn(
              "text-primary",
              isMobile ? "h-4 w-4" : "h-5 w-5 lg:h-6 lg:w-6"
            )} />
            <h2 className={cn(
              "font-semibold",
              FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionTitle
            )}>
              业务分析
            </h2>
          </div>
          <div className={cn(
            "grid gap-4",
            FINANCIAL_RESPONSIVE_CONFIG.charts.gridLayout,
            FINANCIAL_RESPONSIVE_CONFIG.charts.gap
          )}>
            {enabledCharts
              .filter(config => config.category === 'business')
              .map(config => (
                <Card key={config.id} className="overflow-hidden">
                  <CardHeader className={cn(
                    isMobile ? "pb-2" : "pb-4"
                  )}>
                    <CardTitle className={cn(
                      "flex items-center justify-between",
                      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                    )}>
                      <span>{config.name}</span>
                      <Badge
                        variant="secondary"
                        className={cn(
                          isMobile ? "text-xs px-1 py-0" : "text-xs"
                        )}
                      >
                        业务
                      </Badge>
                    </CardTitle>
                    {!isMobile && (
                      <p className={cn(
                        "text-muted-foreground",
                        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                      )}>
                        {config.description}
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className={cn(
                    isMobile ? "pt-0 px-3 pb-3" : "pt-0"
                  )}>
                    {renderChart(config)}
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>
      </div>

      {/* 加载状态遮罩 */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <RefreshCw className="h-5 w-5 animate-spin text-primary" />
              <div className="text-sm font-medium">正在加载图表数据...</div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default FinancialChartsPage
