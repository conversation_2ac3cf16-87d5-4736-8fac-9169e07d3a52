import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/Card'
import { AlertTriangle, DollarSign } from 'lucide-react'
import { formatCurrency, preciseCalculation } from '@/utils/financial'
import type { OrderVO, ActualSettlementItem } from '@/types/financial'

interface SettlementConfirmDialogProps {
  /** 是否显示对话框 */
  open: boolean
  /** 关闭对话框回调 */
  onClose: () => void
  /** 确认结算回调 */
  onConfirm: (items: ActualSettlementItem[]) => void
  /** 要结算的订单列表 */
  orders: OrderVO[]
  /** 是否正在加载 */
  loading?: boolean
}

/**
 * 结算确认对话框
 * 支持单个和批量订单的实际结算金额填写
 */
export const SettlementConfirmDialog: React.FC<SettlementConfirmDialogProps> = ({
  open,
  onClose,
  onConfirm,
  orders,
  loading = false
}) => {
  // 结算项状态
  const [settlementItems, setSettlementItems] = useState<ActualSettlementItem[]>([])
  // 表单错误状态
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 初始化结算项
  useEffect(() => {
    if (open && orders.length > 0) {
      const items: ActualSettlementItem[] = orders.map(order => ({
        payid: order.payid,
        orderDisplay: order.payid,
        calculatedFee: 0, // 不需要显示计算费用
        actualAmount: 0 // 用户手动输入
      }))

      setSettlementItems(items)
      setErrors({})
    }
  }, [open, orders])

  // 更新实际结算金额
  const handleActualAmountChange = (payid: string, value: string) => {
    const numValue = parseFloat(value)
    
    setSettlementItems(prev => 
      prev.map(item => 
        item.payid === payid 
          ? { ...item, actualAmount: isNaN(numValue) ? 0 : numValue }
          : item
      )
    )

    // 清除该字段的错误
    if (errors[payid]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[payid]
        return newErrors
      })
    }
  }

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    settlementItems.forEach(item => {
      if (item.actualAmount <= 0) {
        newErrors[item.payid] = '实际结算金额必须大于0'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 确认结算
  const handleConfirm = () => {
    if (!validateForm()) {
      return
    }
    onConfirm(settlementItems)
  }

  // 计算总金额
  const totalActualAmount = settlementItems.reduce(
    (sum, item) => preciseCalculation.add(sum, item.actualAmount),
    0
  )

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            确认结算金额
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 提示信息 */}
          <div className="flex items-start gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">结算说明：</p>
              <ul className="space-y-1">
                <li>• 以下为未结算的主订单，请输入实际结算金额</li>
                <li>• 实际结算金额必须大于0</li>
                <li>• 确认后将更新订单状态为已结算，子订单将自动跟随</li>
                <li>• 已结算的订单不会重复处理</li>
              </ul>
            </div>
          </div>

          {/* 结算项列表 */}
          <div className="space-y-3">
            {settlementItems.map((item, index) => {
              const order = orders.find(o => o.payid === item.payid)
              return (
                <Card key={item.payid} className="p-4">
                  <div className="grid grid-cols-2 gap-4 items-center">
                    {/* 用户信息 */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">
                        主订单 #{index + 1}
                      </Label>
                      <div className="mt-1">
                        <p className="font-medium text-gray-900">
                          {order?.agentNickname || order?.anchorNickname || '未知用户'}
                        </p>
                      </div>
                    </div>

                    {/* 实际结算金额输入 */}
                    <div>
                      <Label htmlFor={`actual-${item.payid}`} className="text-sm font-medium text-gray-700">
                        实际结算金额 *
                      </Label>
                      <Input
                        id={`actual-${item.payid}`}
                        type="number"
                        step="0.01"
                        min="0.01"
                        value={item.actualAmount || ''}
                        onChange={(e) => handleActualAmountChange(item.payid, e.target.value)}
                        className={errors[item.payid] ? 'border-red-500' : ''}
                        placeholder="请输入实际结算金额"
                      />
                      {errors[item.payid] && (
                        <p className="text-xs text-red-600 mt-1">{errors[item.payid]}</p>
                      )}
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* 合计信息 */}
          {settlementItems.length > 1 && (
            <Card className="p-4 bg-gray-50">
              <div className="text-center">
                <p className="text-sm text-gray-600">实际结算合计</p>
                <p className="text-lg font-semibold text-blue-600">
                  {formatCurrency(totalActualAmount)}
                </p>
              </div>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={loading}>
            {loading ? '结算中...' : `确认结算 ${settlementItems.length} 个订单`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
