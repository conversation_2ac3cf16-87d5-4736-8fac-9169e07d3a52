import React, { useState, useCallback } from 'react'
import { Download, TrendingUp, FileSpreadsheet, FileText, ChevronDown, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Label } from '@/components/ui/label'

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'


import {
  CategoryTabs
} from '@/components/financial'
import { useFinancialStats } from '@/hooks/useFinancialStats'
import { usePageTitle } from '@/router/guards'
import { useToast } from '@/hooks/useToast'
import { cn } from '@/utils'
import { formatTimeRangeText, formatTimeForApi, getPresetTimeRange } from '@/utils/financial'
import { TimeRangePreset } from '@/types/financial'
import { downloadCSV, exportToExcel, validateExportData } from '@/utils/export'
import { FINANCIAL_RESPONSIVE_CONFIG } from '@/constants/responsive'
import { useBreakpoint } from '@/hooks/useBreakpoint'
import type { DateRange } from "react-day-picker"
import type {
  FinancialStatsCategory,
  FinancialStatsItem,
  FinancialStatsRequest,
  TimeRange
} from '@/types/financial'
import { FinancialStatsCategory as FSCategory } from '@/types/financial'
import { DateRangePicker } from "@/components/ui"

/**
 * 将 DateRange 转换为 FinancialStatsRequest 参数
 * 只有当起始日期和结束日期都存在时才返回有效的请求参数
 */
const convertDateRangeToRequest = (dateRange: DateRange | undefined, includeAnchor: boolean): FinancialStatsRequest | null => {
  // 必须同时有起始日期和结束日期才能构成完整的查询请求
  if (!dateRange?.from || !dateRange?.to) {
    return null
  }

  const startTime = formatTimeForApi(dateRange.from.toISOString())
  const endTime = formatTimeForApi(dateRange.to.toISOString())

  return {
    startTime,
    endTime,
    includeAnchor
  }
}



/**
 * 检查日期范围是否完整（包含起始日期和结束日期）
 */
const isDateRangeComplete = (dateRange: DateRange | undefined): boolean => {
  return !!(dateRange?.from && dateRange?.to)
}

/**
 * 检查日期范围是否正在选择中（只有起始日期，没有结束日期）
 */
const isDateRangeSelecting = (dateRange: DateRange | undefined): boolean => {
  return !!(dateRange?.from && !dateRange?.to)
}

/**
 * 财务数据仪表板页面组件
 * 集成时间选择器、主播数据开关和数据展示组件
 */
const FinancialDashboard: React.FC = () => {
  // 设置页面标题
  usePageTitle('财务数据统计')

  const { toast } = useToast()
  const { isMobile, isTablet } = useBreakpoint()

  // 状态管理
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>()
  const [includeAnchor] = useState(true)
  const [activeTab, setActiveTab] = useState<FinancialStatsCategory>('total' as FinancialStatsCategory)

  // 查询控制状态 - 当前生效的查询参数
  const [currentQueryParams, setCurrentQueryParams] = useState<FinancialStatsRequest>(() => ({
    ...getPresetTimeRange(TimeRangePreset.TODAY),
    includeAnchor: true
  }))

  // 处理日期范围确认选择
  const handleDateRangeConfirm = useCallback((dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange?.to) {
      // 验证日期范围
      const validation = validateDateRange(dateRange)
      if (!validation.isValid) {
        toast({
          title: '日期范围无效',
          description: validation.error,
          variant: 'destructive'
        })
        return
      }

      // 转换并设置查询参数
      const queryParams = convertDateRangeToRequest(dateRange, includeAnchor)
      if (queryParams) {
        setCurrentQueryParams(queryParams)
        setSelectedRange(dateRange) // 同步选择状态
        toast({
          title: '查询已更新',
          description: '正在获取新的财务数据...'
        })
      }
    } else if (dateRange === undefined) {
      // 清除选择，使用默认今日范围
      const defaultParams = {
        ...getPresetTimeRange(TimeRangePreset.TODAY),
        includeAnchor
      }
      setCurrentQueryParams(defaultParams)
      setSelectedRange(undefined)
      toast({
        title: '已重置',
        description: '使用默认时间范围查询数据'
      })
    }
  }, [includeAnchor, toast])

  // 检查日期范围状态
  const isRangeComplete = isDateRangeComplete(selectedRange)
  const isRangeSelecting = isDateRangeSelecting(selectedRange)

  /**
   * 验证日期范围是否有效
   */
  const validateDateRange = (dateRange: DateRange | undefined): { isValid: boolean; error?: string } => {
    if (!dateRange?.from || !dateRange?.to) {
      return { isValid: false, error: '请选择完整的日期范围' }
    }

    if (dateRange.to < dateRange.from) {
      return { isValid: false, error: '结束日期不能早于开始日期' }
    }

    // 检查日期范围是否过大（例如：不超过1年）
    const daysDiff = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > 365) {
      return { isValid: false, error: '日期范围不能超过365天' }
    }

    return { isValid: true }
  }



  // 数据获取 - 使用当前查询参数
  const {
    data,
    loading,
    lastUpdated
  } = useFinancialStats(
    currentQueryParams,
    {
      onError: (error) => {
        toast({
          title: '数据获取失败',
          description: error.message,
          variant: 'destructive'
        })
      }
    }
  )
  /**
   * 处理标签页切换
   */
  const handleTabChange = useCallback((category: FinancialStatsCategory) => {
    setActiveTab(category)
  }, [])



  /**
   * 处理数据导出
   */
  const handleExport = useCallback((format: 'csv' | 'excel' = 'csv') => {
    // 检查数据是否存在
    if (!data) {
      toast({
        title: '导出失败',
        description: '暂无数据可导出',
        variant: 'destructive'
      })
      return
    }

    // 验证导出数据
    const validation = validateExportData(data)
    if (!validation.valid) {
      toast({
        title: '导出失败',
        description: validation.message || '数据验证失败',
        variant: 'destructive'
      })
      return
    }

    try {
      // 生成时间范围描述用于文件名
      const timeRange: TimeRange = {
        startTime: currentQueryParams.startTime,
        endTime: currentQueryParams.endTime
      }
      const timeRangeDesc = formatTimeRangeText(timeRange)

      // 执行导出
      if (format === 'excel') {
        exportToExcel(data, {
          format: 'excel',
          categories: [FSCategory.USER, FSCategory.ANCHOR, FSCategory.TOTAL, FSCategory.BUSINESS],
          filename: timeRangeDesc
        })
        toast({
          title: '导出成功',
          description: `已导出Excel格式文件，包含${validation.itemCount}条数据`,
          duration: 3000
        })
      } else {
        downloadCSV(data, {
          format: 'csv',
          categories: [FSCategory.USER, FSCategory.ANCHOR, FSCategory.TOTAL, FSCategory.BUSINESS],
          filename: timeRangeDesc
        })
        toast({
          title: '导出成功',
          description: `已导出CSV格式文件，包含${validation.itemCount}条数据`,
          duration: 3000
        })
      }
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: '导出过程中发生错误，请重试',
        variant: 'destructive'
      })
    }
  }, [data, currentQueryParams, toast])

  /**
   * 处理卡片点击
   */
  const handleCardClick = useCallback((
    _item: FinancialStatsItem,
    _category: FinancialStatsCategory
  ) => {
    // TODO: 实现卡片点击详情功能
  }, [])

  /**
   * 获取数据统计摘要
   */
  const getDataSummary = () => {
    if (!data) return null
    const totalItems = [
      ...data.userStats,
      ...data.anchorStats,
      ...data.totalStats,
      ...data.businessStats
    ].length

    return {
      totalItems,
      categories: {
        user: data.userStats.length,
        anchor: data.anchorStats.length,
        total: data.totalStats.length,
        business: data.businessStats.length
      }
    }
  }

  const summary = getDataSummary()

  return (
    <div className={cn(
      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionSpacing,
      FINANCIAL_RESPONSIVE_CONFIG.pageLayout.containerPadding,
      "min-h-0" // 确保容器可以滚动
    )}>
      {/* 页面标题和操作区域 */}
      <div className={cn(
        "flex items-start justify-between",
        FINANCIAL_RESPONSIVE_CONFIG.pageLayout.controlsLayout
      )}>
        <div className="flex-1 min-w-0">
          <h1 className={cn(
            "font-bold tracking-tight flex items-center",
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.titleSize
          )}>
            <TrendingUp className={cn(
              "mr-2 sm:mr-3 text-primary",
              isMobile ? "w-5 h-5" : "w-6 h-6 sm:w-8 sm:h-8"
            )} />
            财务数据统计
          </h1>
          <p className={cn(
            "text-muted-foreground mt-1 break-words",
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
          )}>
            {isMobile ? "查看平台财务数据" : "查看和分析平台财务数据，支持多维度统计和时间范围筛选"}
          </p>
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center justify-end">

          {/* 导出按钮 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={loading || !data}
                className={cn(
                  "flex items-center",
                  isMobile ? "space-x-1" : "space-x-2"
                )}
              >
                <Download className={cn(
                  isMobile ? "h-3 w-3" : "h-4 w-4"
                )} />
                {!isMobile && <span>导出</span>}
                <ChevronDown className={cn(
                  "ml-1",
                  isMobile ? "h-2 w-2" : "h-3 w-3"
                )} />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className={cn(
                "p-2",
                isMobile ? "w-40" : "w-48"
              )}
              align="end"
            >
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleExport('csv')}
                  disabled={loading || !data}
                  className="w-full justify-start"
                >
                  <FileText className={cn(
                    "mr-2",
                    isMobile ? "h-3 w-3" : "h-4 w-4"
                  )} />
                  <span className={cn(
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    导出为CSV
                  </span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleExport('excel')}
                  disabled={loading || !data}
                  className="w-full justify-start"
                >
                  <FileSpreadsheet className={cn(
                    "mr-2",
                    isMobile ? "h-3 w-3" : "h-4 w-4"
                  )} />
                  <span className={cn(
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    导出为Excel
                  </span>
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader className={cn(
          FINANCIAL_RESPONSIVE_CONFIG.pageLayout.containerPadding
        )}>
          <CardTitle className={cn(
            FINANCIAL_RESPONSIVE_CONFIG.pageLayout.sectionTitle
          )}>
            查询条件
          </CardTitle>
        </CardHeader>
        <CardContent className={cn(
          "space-y-4",
          FINANCIAL_RESPONSIVE_CONFIG.pageLayout.containerPadding
        )}>
          <div className={cn(
            "grid gap-4",
            isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2",
            isMobile ? "gap-4" : "gap-6"
          )}>
            {/* 时间范围选择器 */}
            <div className="space-y-2">
              <Label className={cn(
                "font-medium",
                FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
              )}>
                时间范围
              </Label>
              <DateRangePicker
                enableTime={true}
                timeFormat="HH:mm:ss"
                onDateChange={handleDateRangeConfirm}
                className={cn(
                  isMobile ? "w-full" : "max-w-md"
                )}
              />

              {/* 日期选择状态提示 */}
              {isRangeSelecting && (
                <div className={cn(
                  "text-amber-600 bg-amber-50 px-3 py-2 rounded-md border border-amber-200",
                  FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                )}>
                  <div className={cn(
                    "flex items-center",
                    isMobile ? "gap-1" : "gap-2"
                  )}>
                    <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                    <span>
                      {isMobile ? "请选择结束日期" : "请选择结束日期，然后点击\"确认选择\"按钮"}
                    </span>
                  </div>
                </div>
              )}
              {!isRangeComplete && !isRangeSelecting && (
                <div className={cn(
                  "text-gray-500",
                  FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                )}>
                  当前使用默认时间范围：今日
                </div>
              )}
            </div>
          </div>



          {/* 数据摘要 */}
          {summary && (
            <div className="pt-4 border-t border-border">
              <div className={cn(
                "grid gap-3 text-center",
                isMobile
                  ? "grid-cols-2 sm:grid-cols-3"
                  : "grid-cols-2 md:grid-cols-5",
                isMobile ? "gap-3" : "gap-4"
              )}>
                <div>
                  <div className={cn(
                    "font-bold text-primary",
                    isMobile ? "text-lg" : "text-xl lg:text-2xl"
                  )}>
                    {summary.totalItems}
                  </div>
                  <div className={cn(
                    "text-muted-foreground",
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    总统计项
                  </div>
                </div>
                <div>
                  <div className={cn(
                    "font-bold text-blue-600",
                    isMobile ? "text-lg" : "text-xl lg:text-2xl"
                  )}>
                    {summary.categories.user}
                  </div>
                  <div className={cn(
                    "text-muted-foreground",
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    用户相关
                  </div>
                </div>
                <div>
                  <div className={cn(
                    "font-bold text-green-600",
                    isMobile ? "text-lg" : "text-xl lg:text-2xl"
                  )}>
                    {summary.categories.anchor}
                  </div>
                  <div className={cn(
                    "text-muted-foreground",
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    主播相关
                  </div>
                </div>
                <div>
                  <div className={cn(
                    "font-bold text-purple-600",
                    isMobile ? "text-lg" : "text-xl lg:text-2xl"
                  )}>
                    {summary.categories.total}
                  </div>
                  <div className={cn(
                    "text-muted-foreground",
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    合计统计
                  </div>
                </div>
                <div className={cn(
                  isMobile && "col-span-2 sm:col-span-1"
                )}>
                  <div className={cn(
                    "font-bold text-orange-600",
                    isMobile ? "text-lg" : "text-xl lg:text-2xl"
                  )}>
                    {summary.categories.business}
                  </div>
                  <div className={cn(
                    "text-muted-foreground",
                    FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
                  )}>
                    其他业务
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 最后更新时间 */}
          {lastUpdated && (
            <div className={cn(
              "text-muted-foreground",
              FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize,
              isMobile ? "text-center" : "text-right"
            )}>
              最后更新: {new Date(lastUpdated).toLocaleString('zh-CN')}
            </div>
          )}
        </CardContent>
      </Card>
      {/* 数据展示区域 */}
      <CategoryTabs
        data={data}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        loading={loading}
        gridColumns={isMobile ? 2 : isTablet ? 3 : 4}
        onCardClick={handleCardClick}
        className={cn(
          isMobile ? "min-h-[300px]" : "min-h-[400px]"
        )}
      />

      {/* 加载状态遮罩 */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <Card className={cn(
            isMobile ? "p-4 mx-4" : "p-6"
          )}>
            <div className={cn(
              "flex items-center",
              isMobile ? "space-x-2" : "space-x-3"
            )}>
              <RefreshCw className={cn(
                "animate-spin text-primary",
                isMobile ? "h-4 w-4" : "h-5 w-5"
              )} />
              <div className={cn(
                "font-medium",
                FINANCIAL_RESPONSIVE_CONFIG.pageLayout.descriptionSize
              )}>
                正在加载财务数据...
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default FinancialDashboard