import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, Lock, User, Shield, Cloud } from 'lucide-react'
import { useAuthStore } from '@/stores/auth'
import { cn } from '@/lib/utils'
import { Captcha } from '@/components/common/Captcha'
import { useCaptcha } from '@/stores'
import type { LoginRequest } from '@/types'
import policeIcon from '@/assets/police-Deb_aa6E.png'

/**
 * 登录表单验证Schema
 */
const loginSchema = z.object({
  username: z
    .string()
    .min(1, '请输入用户名')
    .min(4, '用户名至少4个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少6个字符')
    .max(20, '密码最多20个字符'),
  captcha: z.string().optional(),
  captchaKey: z.string().optional(),
  rememberMe: z.boolean().optional(),
})

type LoginFormData = z.infer<typeof loginSchema>

/**
 * 登录页面组件
 */
const Login: React.FC = () => {
  const { login, loading, error, clearError } = useAuthStore()
  const { generateCaptcha, resetRetryCount } = useCaptcha()

  const [showPassword, setShowPassword] = useState(false)
  const [captchaValue, setCaptchaValue] = useState('')
  const [captchaId, setCaptchaId] = useState('')
  const [captchaError, setCaptchaError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      captcha: '',
      captchaKey: '',
      rememberMe: false,
    },
  })

  /**
   * 处理验证码变化
   */
  const handleCaptchaChange = (value: string, id: string) => {
    setCaptchaValue(value)
    setCaptchaId(id)
    setValue('captcha', value)
    setValue('captchaKey', id)

    // 清除验证码错误
    if (captchaError) {
      setCaptchaError('')
    }
  }

  /**
   * 处理验证码错误
   */
  const handleCaptchaError = (error: string) => {
    setCaptchaError(error)
  }

  /**
   * 处理登录提交
   */
  const onSubmit = async (data: LoginFormData) => {
    try {
      clearError()
      setCaptchaError('')

      // 验证验证码是否已填写
      if (!data.captcha || !data.captchaKey) {
        setCaptchaError('请输入验证码')
        return
      }

      const loginData: LoginRequest = {
        username: data.username,
        password: data.password,
        captcha: data.captcha,
        captchaKey: data.captchaKey,
        rememberMe: data.rememberMe,
      }

      await login(loginData)

      // 登录成功后清空验证码输入，让Captcha组件自动刷新
      setCaptchaValue('')
      setValue('captcha', '')
      setValue('captchaKey', '')

    } catch (error) {
      // 登录失败后立即刷新验证码
      try {
        await generateCaptcha()
      } catch (captchaError) {
        console.error('刷新验证码失败:', captchaError)
      }

      // 清空验证码输入
      setCaptchaValue('')
      setValue('captcha', '')
      setValue('captchaKey', '')

      // 如果是验证码相关错误，设置错误信息
      if (error instanceof Error && error.message.includes('验证码')) {
        setCaptchaError(error.message)
        // 重置重试计数，允许用户重新尝试
        resetRetryCount()
      }
    }
  }

  /**
   * 切换密码显示状态
   */
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  // 监听错误变化，自动清除
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError()
      }, 5000)
      return () => clearTimeout(timer)
    }
    return undefined;
  }, [error, clearError])

  // 监听验证码错误，自动清除
  useEffect(() => {
    if (captchaError) {
      const timer = setTimeout(() => {
        setCaptchaError('')
      }, 5000)
      return () => clearTimeout(timer)
    }
    return undefined;
  }, [captchaError])

  // 组件挂载时的错误处理
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('登录页面错误:', event.error)
    }

    window.addEventListener('error', handleError)
    return () => window.removeEventListener('error', handleError)
  }, [])



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800 flex items-center justify-center p-4">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-indigo-200/30 dark:bg-indigo-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative w-full max-w-md">
        {/* 头部 */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg mb-6">
            <Cloud className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            欢迎回到 jCloud
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            安全可靠的云端管理平台
          </p>
        </div>

        {/* 登录卡片 */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* 错误提示 */}
            {error && (
              <div className="rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20 p-4">
                <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                  {error}
                </p>
              </div>
            )}

            {/* 用户名输入框 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                用户名
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('username')}
                  type="text"
                  autoComplete="username"
                  className={cn(
                    "block w-full pl-10 pr-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                    errors.username
                      ? "border-red-300 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  )}
                  placeholder="请输入用户名"
                />
              </div>
              {errors.username && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.username.message}
                </p>
              )}
            </div>

            {/* 密码输入框 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className={cn(
                    "block w-full pl-10 pr-12 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                    errors.password
                      ? "border-red-300 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  )}
                  placeholder="请输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* 验证码输入框 */}
            <div>
              <label htmlFor="captcha" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                验证码
              </label>
              <Captcha
                value={captchaValue}
                captchaId={captchaId}
                onChange={handleCaptchaChange}
                onError={handleCaptchaError}
                placeholder="请输入验证码"
                error={!!(errors.captcha || captchaError)}
                helperText={errors.captcha?.message || captchaError}
                disabled={loading || isSubmitting}
              />
            </div>

            {/* 记住我选项 */}
            <div className="flex items-center">
              <input
                {...register('rememberMe')}
                id="rememberMe"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label htmlFor="rememberMe" className="ml-3 block text-sm text-gray-700 dark:text-gray-300">
                记住我
              </label>
            </div>

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={loading || isSubmitting}
              className={cn(
                "w-full flex justify-center items-center gap-2 py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white",
                "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",
                "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
                "shadow-lg hover:shadow-xl"
              )}
            >
              {(loading || isSubmitting) && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              {loading || isSubmitting ? '登录中...' : (
                <>
                  <Shield className="h-4 w-4" />
                  登录
                </>
              )}
            </button>
          </form>
        </div>
        {/* 版权信息 */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            冀ICP备2024097556号-6
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center gap-1">
            <img src={policeIcon} alt="公安备案" className="w-4 h-4" />
            冀公网安备13040302001687号
          </p>
          <p className="text-sm pt-2 text-gray-500 dark:text-gray-400">
            © 2025 jCloud. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
