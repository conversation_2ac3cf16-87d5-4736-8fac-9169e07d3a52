/**
 * 响应式设计常量配置
 * 统一管理整个应用的响应式设计规范
 */

import type { Breakpoint } from '@/hooks/useBreakpoint'

/**
 * 财务模块响应式配置
 */
export const FINANCIAL_RESPONSIVE_CONFIG = {
  /**
   * 统计卡片配置
   */
  statsCard: {
    /** 网格列数配置 */
    gridCols: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
    /** 卡片间距 */
    gap: 'gap-3 sm:gap-4 lg:gap-6',
    /** 卡片内边距 */
    padding: 'px-4 py-3 sm:px-6 sm:py-4',
    /** 标题文字大小 */
    titleSize: 'text-xs sm:text-sm',
    /** 数值文字大小 */
    valueSize: 'text-lg sm:text-xl lg:text-2xl',
    /** 图标大小 */
    iconSize: 'w-3 h-3 sm:w-4 sm:h-4'
  },

  /**
   * 分类标签页配置
   */
  categoryTabs: {
    /** 标签页文字大小 */
    textSize: 'text-xs sm:text-sm',
    /** 图标大小 */
    iconSize: 'w-3 h-3 sm:w-4 sm:h-4',
    /** 标签页内边距 */
    padding: 'px-2 py-1 sm:px-3 sm:py-2',
    /** 徽章文字大小 */
    badgeSize: 'text-xs'
  },

  /**
   * 页面布局配置
   */
  pageLayout: {
    /** 页面容器内边距 */
    containerPadding: 'px-4 py-4 sm:px-6 sm:py-6',
    /** 页面标题大小 */
    titleSize: 'text-lg sm:text-xl lg:text-2xl',
    /** 页面描述文字大小 */
    descriptionSize: 'text-xs sm:text-sm',
    /** 区块间距 */
    sectionSpacing: 'space-y-4 sm:space-y-6',
    /** 控制区域布局 */
    controlsLayout: 'flex-col sm:flex-row gap-3 sm:gap-4',
    /** 页面容器滚动设置 */
    scrollContainer: 'min-h-0 overflow-y-auto'
  },

  /**
   * 表格配置
   */
  table: {
    /** 表格容器 */
    container: 'overflow-x-auto',
    /** 表格头部文字 */
    headerText: 'text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider',
    /** 表格单元格内边距 */
    cellPadding: 'px-2 py-2 sm:px-4 sm:py-3',
    /** 表格文字大小 */
    textSize: 'text-xs sm:text-sm',
    /** 操作按钮大小 */
    actionButtonSize: 'text-xs sm:text-sm',
    /** 操作图标大小 */
    actionIconSize: 'w-3 h-3 sm:w-4 sm:h-4'
  },

  /**
   * 对话框配置
   */
  dialog: {
    /** 对话框宽度 */
    width: 'max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl',
    /** 对话框最大高度 */
    maxHeight: 'max-h-[80vh] sm:max-h-[85vh]',
    /** 表单字段间距 */
    formSpacing: 'space-y-3 sm:space-y-4',
    /** 标签文字大小 */
    labelSize: 'text-xs sm:text-sm',
    /** 输入框文字大小 */
    inputSize: 'text-sm sm:text-base',
    /** 按钮文字大小 */
    buttonSize: 'text-xs sm:text-sm',
    /** 按钮内边距 */
    buttonPadding: 'px-3 py-1.5 sm:px-4 sm:py-2'
  },

  /**
   * 时间选择器配置
   */
  dateRangePicker: {
    /** 触发按钮高度 */
    triggerHeight: 'h-10 sm:h-11',
    /** 触发按钮内边距 */
    triggerPadding: 'px-3 sm:px-4',
    /** 触发按钮文字大小 */
    triggerTextSize: 'text-sm sm:text-base',
    /** 图标大小 */
    iconSize: 'h-3 w-3 sm:h-4 sm:w-4',
    /** 弹窗宽度 */
    popoverWidth: 'w-[95vw] max-w-sm sm:w-auto',
    /** 快捷选项布局 */
    quickOptionsLayout: 'grid grid-cols-2 gap-1 sm:space-y-1',
    /** 快捷选项按钮大小 */
    quickButtonSize: 'h-7 px-2 text-xs sm:h-8 sm:px-2 sm:text-sm',
    /** 日历月数 */
    calendarMonths: '1 sm:2',
    /** 日历单元格大小 */
    calendarCellSize: 'h-7 w-7 text-xs sm:h-8 sm:w-8 sm:text-sm',
    /** 时间选择器间距 */
    timePickerSpacing: 'space-y-1.5 sm:space-y-2',
    /** 底部按钮布局 */
    bottomButtonLayout: 'justify-center gap-3 sm:justify-between sm:gap-0'
  },

  /**
   * 图表配置
   */
  charts: {
    /** 图表网格布局 */
    gridLayout: 'grid-cols-1 lg:grid-cols-2',
    /** 图表间距 */
    gap: 'gap-4 sm:gap-6',
    /** 图表高度配置 */
    heights: {
      sm: 200,
      md: 250,
      lg: 300,
      xl: 400,
      '2xl': 450
    },
    /** 图表边距 */
    margins: {
      sm: { top: 10, right: 10, bottom: 10, left: 10 },
      md: { top: 15, right: 15, bottom: 15, left: 15 },
      lg: { top: 20, right: 20, bottom: 20, left: 20 },
      xl: { top: 30, right: 30, bottom: 30, left: 30 }
    }
  }
} as const

/**
 * 表格列显示配置
 * 定义在不同断点下哪些列应该显示
 */
export const TABLE_COLUMN_CONFIG = {
  /** 订单管理表格列配置 */
  orderManagement: {
    // 基础信息列 - 所有断点都显示
    checkbox: ['sm', 'md', 'lg', 'xl', '2xl'] as Breakpoint[],
    payid: ['sm', 'md', 'lg', 'xl', '2xl'] as Breakpoint[],
    status: ['sm', 'md', 'lg', 'xl', '2xl'] as Breakpoint[],
    
    // 用户信息 - 中等屏幕以上显示
    userName: ['md', 'lg', 'xl', '2xl'] as Breakpoint[],
    userType: ['lg', 'xl', '2xl'] as Breakpoint[],
    
    // 金额信息 - 所有断点显示
    feeActual: ['sm', 'md', 'lg', 'xl', '2xl'] as Breakpoint[],
    
    // 时间信息 - 大屏幕显示
    createTime: ['lg', 'xl', '2xl'] as Breakpoint[],
    updateTime: ['xl', '2xl'] as Breakpoint[],
    
    // 操作列 - 所有断点显示
    actions: ['sm', 'md', 'lg', 'xl', '2xl'] as Breakpoint[]
  }
} as const

/**
 * 移动端特殊配置
 */
export const MOBILE_CONFIG = {
  /** 移动端表格替代方案 */
  tableAlternative: {
    /** 是否使用卡片式布局替代表格 */
    useCardLayout: true,
    /** 卡片内边距 */
    cardPadding: 'p-4',
    /** 卡片间距 */
    cardSpacing: 'space-y-3',
    /** 卡片圆角 */
    cardRadius: 'rounded-lg'
  },

  /** 移动端导航配置 */
  navigation: {
    /** 是否显示面包屑 */
    showBreadcrumb: false,
    /** 是否简化标题 */
    simplifyTitle: true
  },

  /** 移动端图表配置 */
  charts: {
    /** 是否简化图例 */
    simplifyLegend: true,
    /** 是否隐藏网格 */
    hideGrid: true,
    /** 工具提示简化 */
    simplifyTooltip: true
  }
} as const

/**
 * 响应式断点检查工具
 */
export const RESPONSIVE_UTILS = {
  /** 检查是否为移动端 */
  isMobile: (width: number): boolean => width < 768,
  
  /** 检查是否为平板端 */
  isTablet: (width: number): boolean => width >= 768 && width < 1024,
  
  /** 检查是否为桌面端 */
  isDesktop: (width: number): boolean => width >= 1024,
  
  /** 获取当前设备类型 */
  getDeviceType: (width: number): 'mobile' | 'tablet' | 'desktop' => {
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }
} as const

/**
 * 响应式动画配置
 */
export const RESPONSIVE_ANIMATIONS = {
  /** 移动端减少动画 */
  mobile: {
    duration: 'duration-200',
    easing: 'ease-out'
  },
  
  /** 桌面端正常动画 */
  desktop: {
    duration: 'duration-300',
    easing: 'ease-in-out'
  }
} as const

/**
 * 可访问性配置
 */
export const ACCESSIBILITY_CONFIG = {
  /** 最小触摸目标大小（移动端） */
  minTouchTarget: 'min-h-[44px] min-w-[44px]',
  
  /** 焦点可见性 */
  focusVisible: 'focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-500',
  
  /** 高对比度支持 */
  highContrast: 'contrast-more:border-black contrast-more:text-black'
} as const

export default FINANCIAL_RESPONSIVE_CONFIG
