import { useState, useEffect } from 'react'

/**
 * Tailwind CSS 断点配置
 */
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof BREAKPOINTS
export type BreakpointValue = typeof BREAKPOINTS[Breakpoint]

/**
 * 当前断点信息
 */
export interface BreakpointInfo {
  /** 当前断点 */
  current: Breakpoint
  /** 屏幕宽度 */
  width: number
  /** 是否为移动端 */
  isMobile: boolean
  /** 是否为平板端 */
  isTablet: boolean
  /** 是否为桌面端 */
  isDesktop: boolean
  /** 断点检查函数 */
  isAbove: (breakpoint: Breakpoint) => boolean
  isBelow: (breakpoint: Breakpoint) => boolean
  isExactly: (breakpoint: Breakpoint) => boolean
}

/**
 * 获取当前断点
 */
const getCurrentBreakpoint = (width: number): Breakpoint => {
  if (width >= BREAKPOINTS['2xl']) return '2xl'
  if (width >= BREAKPOINTS.xl) return 'xl'
  if (width >= BREAKPOINTS.lg) return 'lg'
  if (width >= BREAKPOINTS.md) return 'md'
  if (width >= BREAKPOINTS.sm) return 'sm'
  return 'sm' // 默认为最小断点
}

/**
 * 响应式断点检测Hook
 * 
 * @example
 * ```tsx
 * const { current, isMobile, isAbove } = useBreakpoint()
 * 
 * // 根据断点渲染不同内容
 * if (isMobile) {
 *   return <MobileLayout />
 * }
 * 
 * // 检查是否大于某个断点
 * if (isAbove('lg')) {
 *   return <DesktopLayout />
 * }
 * ```
 */
export const useBreakpoint = (): BreakpointInfo => {
  const [width, setWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth
    }
    return BREAKPOINTS.lg // SSR 默认值
  })

  useEffect(() => {
    const handleResize = () => {
      setWidth(window.innerWidth)
    }

    // 添加防抖以提高性能
    let timeoutId: NodeJS.Timeout
    const debouncedHandleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleResize, 100)
    }

    window.addEventListener('resize', debouncedHandleResize)
    
    // 初始化时获取一次
    handleResize()

    return () => {
      window.removeEventListener('resize', debouncedHandleResize)
      clearTimeout(timeoutId)
    }
  }, [])

  const current = getCurrentBreakpoint(width)

  const breakpointInfo: BreakpointInfo = {
    current,
    width,
    isMobile: width < BREAKPOINTS.md,
    isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
    isDesktop: width >= BREAKPOINTS.lg,
    isAbove: (breakpoint: Breakpoint) => width >= BREAKPOINTS[breakpoint],
    isBelow: (breakpoint: Breakpoint) => width < BREAKPOINTS[breakpoint],
    isExactly: (breakpoint: Breakpoint) => {
      const breakpoints = Object.entries(BREAKPOINTS).sort(([, a], [, b]) => a - b)
      const currentIndex = breakpoints.findIndex(([key]) => key === breakpoint)
      
      if (currentIndex === -1) return false
      
      const currentBreakpoint = breakpoints[currentIndex][1]
      const nextBreakpoint = breakpoints[currentIndex + 1]?.[1]
      
      if (nextBreakpoint) {
        return width >= currentBreakpoint && width < nextBreakpoint
      } else {
        return width >= currentBreakpoint
      }
    }
  }

  return breakpointInfo
}

/**
 * 响应式值Hook
 * 根据当前断点返回对应的值
 * 
 * @example
 * ```tsx
 * const columns = useResponsiveValue({
 *   sm: 1,
 *   md: 2,
 *   lg: 3,
 *   xl: 4
 * })
 * ```
 */
export const useResponsiveValue = <T>(values: Partial<Record<Breakpoint, T>>): T | undefined => {
  const { current } = useBreakpoint()
  
  // 按断点大小排序，从大到小查找匹配的值
  const sortedBreakpoints: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm']
  const currentBreakpointIndex = sortedBreakpoints.indexOf(current)
  
  // 从当前断点开始，向下查找第一个有值的断点
  for (let i = currentBreakpointIndex; i < sortedBreakpoints.length; i++) {
    const breakpoint = sortedBreakpoints[i]
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]
    }
  }
  
  return undefined
}

/**
 * 媒体查询Hook
 * 
 * @example
 * ```tsx
 * const isLargeScreen = useMediaQuery('(min-width: 1024px)')
 * ```
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches
    }
    return false
  })

  useEffect(() => {
    const mediaQuery = window.matchMedia(query)
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    setMatches(mediaQuery.matches)

    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [query])

  return matches
}

export default useBreakpoint
