import { useState } from 'react'
import { useMount, useMemoizedFn, useUpdateEffect } from 'ahooks'
import { useAppStore } from '../stores'

/**
 * Store初始化Hook - 基于Context7 + ahooks最佳实践
 * 彻底修复无限循环问题：不在初始化时调用setTheme
 */
export const useStoreInitialization = () => {
  const { theme } = useAppStore() // 只读取theme，不获取setTheme
  const [initialized, setInitialized] = useState(false)

  // 使用ahooks的useMount只在组件挂载时执行一次
  useMount(() => {
    // 直接应用当前主题到DOM，不调用setTheme避免循环
    const root = document.documentElement
    if (theme === 'dark') {
      root.classList.add('dark')
    } else if (theme === 'light') {
      root.classList.remove('dark')
    } else {
      // 系统主题
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (isDark) {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }

    setInitialized(true)
  })

  // 使用ahooks的useUpdateEffect监听主题变化，跳过首次渲染
  // 创建主题变化处理函数
  const handleThemeChange = useMemoizedFn((e: MediaQueryListEvent) => {
    const root = document.documentElement
    if (e.matches) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  })

  useUpdateEffect(() => {
    console.log('主题变化:', theme)

    // 监听系统主题变化
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

      // 添加监听器
      mediaQuery.addEventListener('change', handleThemeChange)

      // 初始设置
      handleThemeChange({ matches: mediaQuery.matches } as MediaQueryListEvent)

      // 清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleThemeChange)
      }
    } else {
      // 非系统主题，直接应用
      const root = document.documentElement
      if (theme === 'dark') {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
      return undefined;
    }
  }, [theme])

  // 返回初始化状态
  return {
    initialized,
    theme,
  }
}
