
import { httpClient } from './request'

/**
 * 用户同步预览数据
 */
interface UserSyncPreviewData {
  previewId: string
  totalCount: number
  newUserCount: number
  existingUserCount: number
  invalidUserCount: number
  usersToSync: VimUser[]
  existingUsers: Record<string, UserConflictInfo>
  invalidUsers: UserValidationError[]
  previewTime: number
  sourceInfo: {
    sourceTable: string
    queryTime: number
    queryCondition: string
    dataVersion: string
  }
}

/**
 * VIM用户数据
 */
 interface VimUser {
  username: string
  phone: string
  identity: number
  email?: string
}

/**
 * 用户冲突信息
 */
interface UserConflictInfo {
  conflictType: 'PHONE_CONFLICT' | 'USERNAME_CONFLICT' | 'EMAIL_CONFLICT' | 'MULTIPLE_CONFLICT' | 'DATA_INCONSISTENT'
  existingUser: any
  syncUser: VimUser
  conflictFields: string[]
  conflictDescription: string
  suggestedAction: string
  autoResolvable: boolean
}

/**
 * 用户验证错误
 */
 interface UserValidationError {
  user: VimUser
  userIdentifier: string
  errorType: 'REQUIRED_FIELD_MISSING' | 'INVALID_FORMAT' | 'LENGTH_EXCEEDED' | 'INVALID_ENUM_VALUE' | 'BUSINESS_RULE_VIOLATION' | 'DATA_INTEGRITY_ERROR'
  errorFields: string[]
  errorMessage: string
  detailMessage?: string
  fixSuggestion?: string
  errorLevel: 'FATAL' | 'ERROR' | 'WARNING' | 'INFO'
  ignorable: boolean
}

/**
 * 同步执行请求
 */
interface UserSyncExecuteRequest {
  previewId: string
  selectedUserPhones?: string[]
  /** 事务模式：STRICT(推荐，性能最佳) */
  transactionMode: 'STRICT'
  batchSize?: number
  skipInvalidUsers?: boolean
  forceOverwrite?: boolean
  conflictStrategy?: 'SKIP' | 'OVERWRITE' | 'MERGE' | 'CREATE_NEW'
  async?: boolean
  remark?: string
}

/**
 * 同步任务状态
 */
interface UserSyncTaskStatus {
  taskId: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  progress: number
  currentStep: string
  totalSteps: number
  message: string
  result?: any
  startTime: number
  endTime?: number
  elapsedTime: number
}

class UserSyncService {
  /**
   * 获取同步预览数据
   */
  static async getSyncPreview(): Promise<UserSyncPreviewData> {
    const response = await httpClient.post<UserSyncPreviewData>('/api/user/sync/preview');
    return response.data;
  }

  /**
   * 验证用户数据
   */
  static async validateUsers(users: VimUser[]): Promise<UserValidationError[]> {
    const response = await httpClient.post<UserValidationError[]>('/api/user/sync/validate', users);
    return response.data;
  }

  /**
   * 检测用户冲突
   */
  static async detectConflicts(users: VimUser[]): Promise<UserConflictInfo[]> {
    const response = await httpClient.post<UserConflictInfo[]>('/api/user/sync/conflicts', users);
    return response.data;
  }

  /**
   * 执行同步（异步）
   */
  static async executeSyncAsync(request: UserSyncExecuteRequest): Promise<string> {
    const response = await httpClient.post<string>('/api/user/sync/execute/async', request);
    return response.data;
  }

  /**
   * 执行同步（同步）
   */
  static async executeSyncSync(request: UserSyncExecuteRequest): Promise<any> {
    const response = await httpClient.post<any>('/api/user/sync/execute/sync', request);
    return response.data;
  }

  /**
   * 获取同步任务状态
   */
  static async getSyncTaskStatus(taskId: string): Promise<UserSyncTaskStatus> {
    const response = await httpClient.get<UserSyncTaskStatus>(`/api/user/sync/status/${taskId}`);
    return response.data;
  }

  /**
   * 取消同步任务
   */
  static async cancelSyncTask(taskId: string): Promise<boolean> {
    const response = await httpClient.post<boolean>(`/api/user/sync/cancel/${taskId}`);
    return response.data;
  }
}

export default UserSyncService;
export type { 
  UserSyncPreviewData, 
  VimUser, 
  UserConflictInfo, 
  UserValidationError, 
  UserSyncExecuteRequest, 
  UserSyncTaskStatus 
};
