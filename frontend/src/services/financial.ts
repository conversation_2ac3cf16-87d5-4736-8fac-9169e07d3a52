import {httpClient} from './request.ts'
import type {
  FinancialStatsRequest,
  GroupedFinancialStatsResponse,
  OrderCreateRequest,
  OrderQueryRequest,
  OrderStatistics,
  OrderVO,
  PageResult,
  ActualSettlementItem,
  BatchSettleRequest,

} from '@/types'

/**
 * 财务数据API服务
 * 提供财务统计数据的查询接口
 */
export class FinancialService {
  /**
   * 获取财务统计数据
   * 根据时间范围和选项获取财务统计数据
   * @param request 查询请求参数
   * @returns 分组财务统计数据
   */
  static async getFinancialStats(request: FinancialStatsRequest): Promise<GroupedFinancialStatsResponse> {
    return httpClient.post<GroupedFinancialStatsResponse>('/financial/stats/query', request)
  }

  /**
   * 获取今日财务数据
   * 获取今日财务统计数据的快捷接口
   * @param includeAnchor 是否包含主播数据，默认为true
   * @returns 分组财务统计数据
   */
  static async getTodayStats(includeAnchor: boolean = true): Promise<GroupedFinancialStatsResponse> {
    return httpClient.get<GroupedFinancialStatsResponse>('/financial/stats/today', {
      params: { includeAnchor }
    })
  }

  /**
   * 验证时间范围参数
   * 客户端验证时间范围的有效性
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 验证结果
   */
  static validateTimeRange(startTime: string, endTime: string): {
    valid: boolean
    error?: string
  } {
    if (!startTime || !endTime) {
      return {
        valid: false,
        error: '开始时间和结束时间不能为空'
      }
    }

    const start = new Date(startTime)
    const end = new Date(endTime)

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return {
        valid: false,
        error: '时间格式不正确'
      }
    }

    if (start >= end) {
      return {
        valid: false,
        error: '结束时间必须晚于开始时间'
      }
    }

    // 限制查询范围不超过1年
    const maxDays = 365
    const diffTime = end.getTime() - start.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > maxDays) {
      return {
        valid: false,
        error: `查询时间范围不能超过${maxDays}天`
      }
    }

    return { valid: true }
  }

  /**
   * 格式化时间参数
   * 将时间字符串格式化为后端需要的格式
   * @param timeStr 时间字符串
   * @returns 格式化后的时间字符串
   */
  static formatTimeParam(timeStr: string): string {
    const date = new Date(timeStr)
    if (isNaN(date.getTime())) {
      throw new Error('无效的时间格式')
    }
    
    // 格式化为 YYYY-MM-DD HH:mm:ss 格式
    return date.toISOString().slice(0, 19).replace('T', ' ')
  }

  /**
   * 构建今日时间范围请求
   * 创建今日时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildTodayRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

    return {
      startTime: this.formatTimeParam(today.toISOString()),
      endTime: this.formatTimeParam(new Date(tomorrow.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建昨日时间范围请求
   * 创建昨日时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildYesterdayRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    return {
      startTime: this.formatTimeParam(yesterday.toISOString()),
      endTime: this.formatTimeParam(new Date(today.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建本周时间范围请求
   * 创建本周时间范围的查询请求（周一到周日）
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildThisWeekRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const dayOfWeek = now.getDay() || 7 // 将周日(0)转换为7
    const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1)
    const nextMonday = new Date(monday.getTime() + 7 * 24 * 60 * 60 * 1000)

    return {
      startTime: this.formatTimeParam(monday.toISOString()),
      endTime: this.formatTimeParam(new Date(nextMonday.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建本月时间范围请求
   * 创建本月时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildThisMonthRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)

    return {
      startTime: this.formatTimeParam(firstDay.toISOString()),
      endTime: this.formatTimeParam(new Date(nextMonth.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }
}

/**
 * 佣金结算订单API服务
 * 提供订单管理的完整接口
 */
export class OrderService {
  /**
   * 创建佣金结算订单
   * @param request 订单创建请求
   * @returns 创建的订单列表
   */
  static async createOrders(request: OrderCreateRequest): Promise<OrderVO[]> {
    console.log('🚀 发送创建订单请求:', request)
    const response = await httpClient.post<OrderVO[]>('/api/orders', request)
    console.log('✅ 创建订单响应:', response)
    return response
  }

  /**
   * 分页查询订单列表
   * @param request 查询请求
   * @returns 分页结果
   */
  static async pageOrders(request: OrderQueryRequest): Promise<PageResult<OrderVO>> {
    console.log('🚀 发送订单分页查询请求:', request)
    const response = await httpClient.get<PageResult<OrderVO>>('/api/orders', {
      params: request
    })
    console.log('✅ 订单分页查询响应:', response)
    // 响应拦截器已经自动解包了data.data，所以直接返回response
    return response
  }

  /**
   * 查询订单详情
   * @param payid 订单ID
   * @returns 订单详情
   */
  static async getOrderDetail(payid: string): Promise<OrderVO> {
    const response = await httpClient.get<OrderVO>(`/api/orders/${payid}`)
    return response
  }

  /**
   * 查询主订单及其子订单
   * @param parentsPayid 主订单ID
   * @returns 主订单及子订单
   */
  static async getMainOrderWithSubOrders(parentsPayid: string): Promise<OrderVO> {
    const response = await httpClient.get<OrderVO>(`/api/orders/${parentsPayid}/tree`)
    return response
  }

  /**
   * 获取指定主订单的子订单列表
   * @param mainOrderId 主订单ID
   * @returns 子订单列表
   */
  static async getSubOrders(mainOrderId: string): Promise<OrderVO[]> {
    const response = await httpClient.get<OrderVO[]>(`/api/orders/${mainOrderId}/sub-orders`)
    return response
  }

  /**
   * 批量结算订单
   * @param payids 订单ID列表
   * @returns 结算成功的订单数量
   */
  static async settleOrders(payids: string[]): Promise<number> {
    const response = await httpClient.put<number>('/api/orders/settle', payids)
    return response
  }

  /**
   * 批量结算订单（带实际结算金额）
   * @param items 结算项列表
   * @returns 结算成功的订单数量
   */
  static async settleOrdersWithActualAmount(items: ActualSettlementItem[]): Promise<number> {
    const request: BatchSettleRequest = { items }
    const response = await httpClient.put<number>('/api/orders/settle-with-amount', request)
    return response
  }

  /**
   * 取消订单
   * @param payid 订单ID
   * @returns 是否成功
   */
  static async cancelOrder(payid: string): Promise<boolean> {
    const response = await httpClient.put<boolean>(`/api/orders/${payid}/cancel`)
    return response
  }

  /**
   * 批量取消订单
   * @param payids 订单ID列表
   * @returns 取消成功的订单数量
   */
  static async cancelOrders(payids: string[]): Promise<number> {
    const response = await httpClient.put<number>('/api/orders/cancel', payids)
    return response
  }

  /**
   * 检查是否可以创建订单
   * @param request 订单创建请求
   * @returns 是否可以创建
   */
  static async canCreateOrder(request: OrderCreateRequest): Promise<boolean> {
    console.log('🚀 发送订单创建检查请求:', request)
    const response = await httpClient.post<boolean>('/api/orders/check', request)
    console.log('✅ 订单创建检查响应:', response)
    return response
  }

  /**
   * 获取订单统计信息
   * @returns 统计信息
   */
  static async getOrderStatistics(): Promise<OrderStatistics> {
    const response = await httpClient.get<OrderStatistics>('/api/orders/statistics')
    return response
  }

  /**
   * 导出订单数据
   * @param request 查询请求
   * @returns 订单数据
   */
  static async exportOrders(request: OrderQueryRequest): Promise<OrderVO[]> {
    const response = await httpClient.post<OrderVO[]>('/api/orders/export', request)
    return response
  }

  /**
   * 导出订单数据为Excel文件
   * @param request 查询请求
   * @returns Excel文件Blob
   */
  static async exportOrdersToExcel(request: OrderQueryRequest): Promise<Blob> {
    // 使用项目的统一HTTP客户端，响应拦截器会自动处理blob响应
    const blob = await httpClient.instance.post('/api/orders/export/excel', request, {
      responseType: 'blob', // 重要：指定响应类型为blob
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/octet-stream, */*',
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时，Excel生成可能需要较长时间
    }) as Blob

    // 验证响应是否为有效的blob
    if (!blob || blob.size === 0) {
      throw new Error('服务器返回的Excel文件为空')
    }

    return blob
  }

  /**
   * 格式化订单状态
   * @param status 状态代码
   * @returns 状态描述
   */
  static formatOrderStatus(status: number): string {
    const statusMap: Record<number, string> = {
      0: '已创建',
      1: '已计算',
      2: '待结算',
      3: '已结算',
      4: '已取消'
    }
    return statusMap[status] || '未知状态'
  }

  /**
   * 格式化结算状态
   * @param status 状态代码
   * @returns 状态描述
   */
  static formatSettlementStatus(status: number): string {
    const statusMap: Record<number, string> = {
      0: '无需结算',
      1: '待处理',
      2: '结算成功',
      3: '结算失败'
    }
    return statusMap[status] || '未知状态'
  }

  /**
   * 格式化金额
   * @param amount 金额
   * @returns 格式化后的金额字符串
   */
  static formatAmount(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  /**
   * 格式化时间戳
   * @param timestamp 时间戳（秒）
   * @returns 格式化后的时间字符串
   */
  static formatTimestamp(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  /**
   * 验证订单创建请求
   * @param request 订单创建请求
   * @returns 验证结果
   */
  static validateCreateRequest(request: OrderCreateRequest): {
    valid: boolean
    error?: string
  } {
    if (!request.startTime || !request.endTime) {
      return { valid: false, error: '开始时间和结束时间不能为空' }
    }

    if (request.startTime >= request.endTime) {
      return { valid: false, error: '结束时间必须晚于开始时间' }
    }

    const maxDays = 365
    const diffDays = Math.ceil((request.endTime - request.startTime) / (24 * 60 * 60))
    if (diffDays > maxDays) {
      return { valid: false, error: `时间范围不能超过${maxDays}天` }
    }

    if (!request.feeRate || request.feeRate <= 0 || request.feeRate > 100) {
      return { valid: false, error: '劳务比例必须在0.01%到100%之间' }
    }

    if (!request.feePerson?.trim()) {
      return { valid: false, error: '收款人姓名不能为空' }
    }

    if (!request.feeAccount?.trim()) {
      return { valid: false, error: '收款账户信息不能为空' }
    }

    return { valid: true }
  }

  // ==================== K线图相关API ====================


}