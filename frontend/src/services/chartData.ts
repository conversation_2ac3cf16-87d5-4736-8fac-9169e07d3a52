import {BusinessDataService} from './businessData'
import {httpClient} from './request'
import type {FinancialStatsRequest} from '@/types'
import type {
  BoxConsumptionData,
  HeatmapDataPoint,
  RevenueDataPoint,
  UserBehaviorScatterData
} from '@/components/charts'

/**
 * 业务类型映射
 * 基于数据库vim_order_box表的type字段
 */
export const BUSINESS_TYPE_MAP = {
  1: { name: '开箱', color: '#3b82f6' },
  2: { name: '兑换', color: '#f59e0b' },
  3: { name: '对战', color: '#10b981' },
  5: { name: '升级', color: '#8b5cf6' },
  6: { name: '补偿', color: '#6b7280' },
  7: { name: 'Roll房', color: '#06b6d4' },
  8: { name: '奖励', color: '#f97316' },
  9: { name: '融合', color: '#84cc16' },
  // 天选业务（预留）
  4: { name: '天选', color: '#ef4444' }
} as const

/**
 * 图表数据服务
 * 整合多数据源，提供统一的图表数据接口
 */
export class ChartDataService {
  /**
   * 获取收入趋势数据
   */
  static async getRevenueTrendData(request: FinancialStatsRequest): Promise<RevenueDataPoint[]> {
    try {
      // 并行获取各类数据
      const [businessData, rechargeData, commissionData] = await Promise.all([
        BusinessDataService.getBusinessRevenueTrend(request),
        BusinessDataService.getRechargeTrend(request),
        BusinessDataService.getCommissionTrend(request)
      ])

      // 整合数据生成收入趋势
      return this.combineRevenueData(businessData, rechargeData, commissionData)
    } catch (error) {
      console.error('获取收入趋势数据失败:', error)
      throw new Error(`获取收入趋势数据失败: ${error}`)
    }
  }



  /**
   * 获取用户活跃度热力图数据
   */
  static async getUserActivityHeatmapData(request: FinancialStatsRequest): Promise<HeatmapDataPoint[]> {
    try {
      // 调用真实的API接口获取用户活跃度数据
      return await httpClient.post<HeatmapDataPoint[]>('/api/business/user/activity-heatmap', request)
    } catch (error) {
      console.error('获取用户活跃度数据失败:', error)
      throw new Error(`获取用户活跃度数据失败: ${error}`)
    }
  }



  /**
   * 获取开箱消费趋势数据
   */
  static async getBoxConsumptionData(request: FinancialStatsRequest): Promise<BoxConsumptionData[]> {
    try {
      // 调用真实的API接口获取开箱消费数据
      return await httpClient.post<BoxConsumptionData[]>('/api/business/box/consumption-trend', request)
    } catch (error) {
      console.error('获取开箱消费数据失败:', error)
      throw new Error(`获取开箱消费数据失败: ${error}`)
    }
  }



  /**
   * 获取用户行为散点图数据
   */
  static async getUserBehaviorScatterData(request: FinancialStatsRequest): Promise<UserBehaviorScatterData[]> {
    try {
      // 调用真实的API接口获取用户充值行为数据
      return await httpClient.post<UserBehaviorScatterData[]>('/api/business/user/behavior-stats', request)
    } catch (error) {
      console.error('获取用户行为数据失败:', error)
      throw new Error(`获取用户行为数据失败: ${error}`)
    }
  }





  /**
   * 整合业务数据生成收入趋势
   */
  private static combineRevenueData(
    businessData: any[],
    rechargeData: any[],
    commissionData: any[]
  ): RevenueDataPoint[] {
    // 按日期分组数据
    const dateMap = new Map<string, any>()

    // 处理充值数据
    rechargeData.forEach(item => {
      if (!dateMap.has(item.date)) {
        dateMap.set(item.date, {
          date: item.date,
          rechargeRevenue: item.totalAmount,
          // 初始化需要的业务类型收入为0
          openBoxRevenue: 0,
          exchangeRevenue: 0,
          battleRevenue: 0,
          timestamp: new Date(item.date).getTime()
        })
      }
    })

    // 处理业务数据
    businessData.forEach(item => {
      const dateData = dateMap.get(item.date) || {
        date: item.date,
        rechargeRevenue: 0,
        // 初始化需要的业务类型收入为0
        openBoxRevenue: 0,
        exchangeRevenue: 0,
        battleRevenue: 0,
        timestamp: new Date(item.date).getTime()
      }

      // 根据业务类型分配收入
      const businessType = BUSINESS_TYPE_MAP[item.businessType as keyof typeof BUSINESS_TYPE_MAP]
      if (businessType) {
        const key = this.getRevenueKey(item.businessType)
        dateData[key] = (dateData[key] || 0) + item.totalAmount
      }

      dateMap.set(item.date, dateData)
    })

    // 处理佣金数据
    commissionData.forEach(item => {
      const dateData = dateMap.get(item.date)
      if (dateData) {
        dateData.commissionExpense = item.totalCommission
        dateData.netRevenue = dateData.rechargeRevenue - item.totalCommission
      }
    })

    // 转换为数组并排序
    return Array.from(dateMap.values())
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(item => ({
        ...item,
        // 确保所有字段都有默认值
        openBoxRevenue: item.openBoxRevenue || 0,
        exchangeRevenue: item.exchangeRevenue || 0,
        battleRevenue: item.battleRevenue || 0,
        tianxuanRevenue: item.tianxuanRevenue || 0,
        upgradeRevenue: item.upgradeRevenue || 0,
        rollRoomRevenue: item.rollRoomRevenue || 0,
        fusionRevenue: item.fusionRevenue || 0,
        rewardRevenue: item.rewardRevenue || 0,
        compensationRevenue: item.compensationRevenue || 0,
        commissionExpense: item.commissionExpense || 0,
        netRevenue: item.netRevenue || item.rechargeRevenue || 0
      }))
  }

  /**
   * 根据业务类型获取对应的收入字段名
   */
  private static getRevenueKey(businessType: number): string {
    const keyMap: Record<number, string> = {
      1: 'openBoxRevenue',
      2: 'exchangeRevenue',
      3: 'battleRevenue',
      4: 'tianxuanRevenue',
      5: 'upgradeRevenue',
      6: 'compensationRevenue',
      7: 'rollRoomRevenue',
      8: 'rewardRevenue',
      9: 'fusionRevenue'
    }
    return keyMap[businessType] || 'otherRevenue'
  }
}

export default ChartDataService
