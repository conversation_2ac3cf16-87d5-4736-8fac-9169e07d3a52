import type {FinancialStatsRequest} from '@/types'
import {httpClient} from "@/services/request.ts";

/**
 * 业务数据接口
 */
export interface BusinessRevenueData {
  date: string
  businessType: number
  orderCount: number
  totalAmount: number
  avgAmount: number
}

/**
 * 充值数据接口
 */
export interface RechargeData {
  date: string
  rechargeCount: number
  totalAmount: number
  avgAmount: number
}

/**
 * 佣金数据接口
 */
export interface CommissionData {
  date: string
  commissionOrders: number
  totalRechargeAmount: number
  totalCommission: number
  avgCommissionRate: number
}

/**
 * 业务数据服务
 * 提供基于真实数据库的业务数据查询
 */
export class BusinessDataService {
  
  /**
   * 获取业务收入趋势数据
   * 基于vim_order_box表的真实数据
   */
  static async getBusinessRevenueTrend(request: FinancialStatsRequest): Promise<BusinessRevenueData[]> {
    try {
      // 调用真实的API接口获取业务收入数据
      return await httpClient.post<BusinessRevenueData[]>('/api/business/revenue-trend', request)
    } catch (error) {
      console.error('获取业务收入趋势数据失败:', error)
      throw new Error(`获取业务收入趋势数据失败: ${error}`)
    }
  }

  /**
   * 获取充值数据
   * 基于vim_order_recharge表的真实数据
   */
  static async getRechargeTrend(request: FinancialStatsRequest): Promise<RechargeData[]> {
    try {
      // 调用真实的API接口获取充值数据
      return await httpClient.post<RechargeData[]>('/api/business/recharge-trend', request)
    } catch (error) {
      console.error('获取充值趋势数据失败:', error)
      throw new Error(`获取充值趋势数据失败: ${error}`)
    }
  }

  /**
   * 获取佣金数据
   * 基于sys_order表的真实数据
   */
  static async getCommissionTrend(request: FinancialStatsRequest): Promise<CommissionData[]> {
    try {
      // 调用真实的API接口获取佣金数据
      return await httpClient.post<CommissionData[]>('/api/business/commission-trend', request)
    } catch (error) {
      console.error('获取佣金趋势数据失败:', error)
      throw new Error(`获取佣金趋势数据失败: ${error}`)
    }
  }


}
