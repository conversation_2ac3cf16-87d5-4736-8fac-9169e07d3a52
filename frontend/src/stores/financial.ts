import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { FinancialService } from '@/services'
import type {
  FinancialStatsRequest,
  GroupedFinancialStatsResponse,
} from '@/types'


/**
 * 财务数据状态接口
 */
interface FinancialState {
  // ==================== 基础财务统计状态 ====================
  
  // 财务统计数据
  statsData: GroupedFinancialStatsResponse | null
  statsLoading: boolean
  statsError: string | null
  
  // 查询参数
  timeRange: {
    startTime: string
    endTime: string
  }
  includeAnchor: boolean
  

  
  // ==================== 基础财务统计操作 ====================
  
  /**
   * 获取财务统计数据
   */
  fetchStatsData: (request: FinancialStatsRequest) => Promise<void>
  
  /**
   * 获取今日财务数据
   */
  fetchTodayStats: (includeAnchor?: boolean) => Promise<void>
  
  /**
   * 设置时间范围
   */
  setTimeRange: (range: { startTime: string; endTime: string }) => void
  
  /**
   * 设置是否包含主播数据
   */
  setIncludeAnchor: (include: boolean) => void
  
  /**
   * 清除财务统计错误
   */
  clearStatsError: () => void
  

  
  // ==================== 通用操作 ====================
  
  /**
   * 清除所有错误
   */
  clearAllErrors: () => void
  
  /**
   * 重置所有状态
   */
  resetAllState: () => void
}

/**
 * 财务数据状态管理
 */
export const useFinancialStore = create<FinancialState>()(
  devtools(
    (set) => ({
      // ==================== 初始状态 ====================
      
      // 基础财务统计状态
      statsData: null,
      statsLoading: false,
      statsError: null,
      timeRange: {
        startTime: '',
        endTime: ''
      },
      includeAnchor: true,
      

      
      // ==================== 基础财务统计操作实现 ====================
      
      fetchStatsData: async (request) => {
        set({ statsLoading: true, statsError: null })
        try {
          const data = await FinancialService.getFinancialStats(request)
          set({ 
            statsData: data, 
            statsLoading: false,
            timeRange: {
              startTime: request.startTime,
              endTime: request.endTime
            },
            includeAnchor: request.includeAnchor ?? true
          })
        } catch (error) {
          set({ 
            statsError: error instanceof Error ? error.message : '获取财务数据失败',
            statsLoading: false 
          })
        }
      },
      
      fetchTodayStats: async (includeAnchor = true) => {
        set({ statsLoading: true, statsError: null })
        try {
          const data = await FinancialService.getTodayStats(includeAnchor)
          set({ 
            statsData: data, 
            statsLoading: false,
            includeAnchor
          })
        } catch (error) {
          set({ 
            statsError: error instanceof Error ? error.message : '获取今日财务数据失败',
            statsLoading: false 
          })
        }
      },
      
      setTimeRange: (range) => set({ timeRange: range }),
      
      setIncludeAnchor: (include) => set({ includeAnchor: include }),
      
      clearStatsError: () => set({ statsError: null }),
      

      
      // ==================== 通用操作实现 ====================
      
      clearAllErrors: () => set({
        statsError: null
      }),
      
      resetAllState: () => set({
        statsData: null,
        statsLoading: false,
        statsError: null,
        timeRange: { startTime: '', endTime: '' },
        includeAnchor: true
      })
    }),
    { 
      name: 'financial-store',
      // 开发环境下启用Redux DevTools
      enabled: process.env.NODE_ENV === 'development'
    }
  )
)
