/**
 * 财务数据相关类型定义
 * 对应后端财务统计功能的数据结构
 */

/**
 * 时间范围接口
 */
export interface TimeRange {
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
}

/**
 * 财务统计查询请求参数
 * 对应后端 FinancialStatsRequest
 */
export interface FinancialStatsRequest extends TimeRange {
  /** 是否包含主播数据 */
  includeAnchor: boolean
}

/**
 * 财务统计数据项
 * 对应后端 FinancialStatsResponse
 */
export interface FinancialStatsItem {
  /** 统计分类 */
  category: string
  /** 统计项名称 */
  statName: string
  /** 统计值 */
  statValue: number
  /** 单位 */
  unit: string
}

/**
 * 分组财务统计响应数据
 * 对应后端 GroupedFinancialStatsResponse
 */
export interface GroupedFinancialStatsResponse {
  /** 用户相关统计 */
  userStats: FinancialStatsItem[]
  /** 主播相关统计 */
  anchorStats: FinancialStatsItem[]
  /** 合计统计 */
  totalStats: FinancialStatsItem[]
  /** 其他业务统计 */
  businessStats: FinancialStatsItem[]
}

/**
 * 财务统计分类枚举
 */
export enum FinancialStatsCategory {
  /** 用户相关统计 */
  USER = 'user',
  /** 主播相关统计 */
  ANCHOR = 'anchor',
  /** 合计统计 */
  TOTAL = 'total',
  /** 其他业务统计 */
  BUSINESS = 'business'
}

/**
 * 时间范围预设选项
 */
export enum TimeRangePreset {
  /** 今天 */
  TODAY = 'today',
  /** 昨天 */
  YESTERDAY = 'yesterday',
  /** 本周 */
  THIS_WEEK = 'thisWeek',
  /** 上周 */
  LAST_WEEK = 'lastWeek',
  /** 本月 */
  THIS_MONTH = 'thisMonth',
  /** 上月 */
  LAST_MONTH = 'lastMonth',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 时间范围选择器选项
 */
export interface TimeRangeOption {
  /** 选项标签 */
  label: string
  /** 选项值 */
  value: TimeRangePreset
  /** 时间范围 */
  range: TimeRange
}

/**
 * 财务数据加载状态
 */
export interface FinancialDataState {
  /** 数据 */
  data: GroupedFinancialStatsResponse | null
  /** 加载中 */
  loading: boolean
  /** 错误信息 */
  error: Error | null
  /** 最后更新时间 */
  lastUpdated: string | null
}

/**
 * 财务数据查询参数
 */
export interface FinancialQueryParams extends FinancialStatsRequest {
  /** 自动刷新间隔（毫秒） */
  refreshInterval?: number
  /** 是否启用缓存 */
  enableCache?: boolean
}

/**
 * 统计卡片数据
 */
export interface StatsCardData {
  /** 标题 */
  title: string
  /** 值 */
  value: number | string
  /** 单位 */
  unit?: string
  /** 格式化后的值 */
  formattedValue?: string
  /** 图标 */
  icon?: string
  /** 颜色主题 */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 趋势 */
  trend?: {
    /** 趋势方向 */
    direction: 'up' | 'down' | 'stable'
    /** 变化值 */
    value: number
    /** 变化百分比 */
    percentage: number
  }
}

/**
 * 财务数据表格列配置
 */
export interface FinancialTableColumn {
  /** 列键 */
  key: string
  /** 列标题 */
  title: string
  /** 数据索引 */
  dataIndex: keyof FinancialStatsItem
  /** 列宽 */
  width?: number
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义渲染函数 */
  render?: (value: any, record: FinancialStatsItem, index: number) => React.ReactNode
}

/**
 * 财务数据导出选项
 */
export interface FinancialExportOptions {
  /** 导出格式 */
  format: 'excel' | 'csv' | 'pdf'
  /** 包含的分类 */
  categories: FinancialStatsCategory[]
  /** 文件名 */
  filename?: string
  /** 是否包含图表 */
  includeCharts?: boolean
}

/**
 * 财务数据权限
 */
export interface FinancialPermissions {
  /** 查看权限 */
  view: boolean
  /** 导出权限 */
  export: boolean
  /** 刷新权限 */
  refresh: boolean
  /** 历史数据查看权限 */
  viewHistory: boolean
}

/**
 * 财务页面配置
 */
export interface FinancialPageConfig {
  /** 默认时间范围 */
  defaultTimeRange: TimeRangePreset
  /** 默认是否包含主播数据 */
  defaultIncludeAnchor: boolean
  /** 自动刷新间隔（毫秒） */
  autoRefreshInterval: number
  /** 是否启用自动刷新 */
  enableAutoRefresh: boolean
  /** 每页显示数量 */
  pageSize: number
  /** 是否显示趋势 */
  showTrend: boolean
}

// ==================== 佣金结算订单相关类型 ====================

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  /** 已创建 */
  CREATED = 0,
  /** 已计算 */
  CALCULATED = 1,
  /** 待结算 */
  PENDING_SETTLEMENT = 2,
  /** 已结算 */
  SETTLED = 3,
  /** 已取消 */
  CANCELLED = 4
}

/**
 * 结算状态枚举
 */
export enum SettlementStatus {
  /** 无需结算 */
  NONE = 0,
  /** 待处理 */
  PENDING = 1,
  /** 结算成功 */
  SUCCESS = 2,
  /** 结算失败 */
  FAILED = 3
}

/**
 * 订单创建请求
 */
export interface OrderCreateRequest {
  /** 结算开始时间（时间戳，秒） */
  startTime: number
  /** 结算结束时间（时间戳，秒） */
  endTime: number
  /** 劳务比例（百分比） */
  feeRate: number
  /** 收款人姓名 */
  feePerson: string
  /** 收款账户信息 */
  feeAccount: string
  /** 目标用户ID（要为哪个用户创建订单） */
  targetUserId: number
}

/**
 * 订单查询请求
 */
export interface OrderQueryRequest {
  /** 页码 */
  pageNum: number
  /** 每页大小 */
  pageSize: number
  /** 订单ID */
  payid?: string
  /** 父订单ID */
  parentsPayid?: string
  /** 代理ID */
  agent?: string
  /** 主播ID */
  anchor?: string
  /** 订单状态 */
  orderStatus?: number
  /** 结算状态 */
  settlementStatus?: number
  /** 创建开始时间 */
  createTimeStart?: number
  /** 创建结束时间 */
  createTimeEnd?: number
  /** 更新开始时间 */
  updateTimeStart?: number
  /** 更新结束时间 */
  updateTimeEnd?: number
  /** 是否只查询主订单 */
  mainOrderOnly?: boolean
  /** 是否只查询子订单 */
  subOrderOnly?: boolean
  /** 选中的订单ID列表（用于导出） */
  selectedOrderIds?: string[]
}

/**
 * 订单展示对象
 */
export interface OrderVO {
  /** 结算订单ID */
  payid: string
  /** 上级关联订单ID */
  parentsPayid?: string
  /** 代理ID */
  agent?: string
  /** 代理昵称 */
  agentNickname?: string
  /** 主播ID */
  anchor?: string
  /** 主播昵称 */
  anchorNickname?: string
  /** 自充值金额 */
  amountSelf: number
  /** 推广充值金额 */
  amountFans: number
  /** 总充值金额 */
  totalAmount: number
  /** 劳务比例 */
  feeRate: number
  /** 推广劳务费 */
  feeValue: number
  /** 实际结算金额 */
  feeActual: number
  /** 实际结算金额（用户手动输入） */
  actualSettlementAmount: number
  /** 收款人 */
  feePerson: string
  /** 收款账号 */
  feeAccount: string
  /** 订单状态代码 */
  orderStatus: number
  /** 订单状态描述 */
  orderStatusDesc: string
  /** 结算状态代码 */
  settlementStatus: number
  /** 结算状态描述 */
  settlementStatusDesc: string
  /** 结算开始时间 */
  settlementStartTime?: number
  /** 结算结束时间 */
  settlementEndTime?: number
  /** 创建时间 */
  createTime: number
  /** 更新时间 */
  updateTime: number
  /** 是否为主订单 */
  isMainOrder: boolean
  /** 子订单列表 */
  subOrders?: OrderVO[]

  /** 前端展开状态（不从后端返回） */
  expanded?: boolean
}

/**
 * 订单统计信息
 */
export interface OrderStatistics {
  /** 总订单数 */
  totalOrders: number
  /** 待结算订单数 */
  pendingOrders: number
  /** 已结算订单数 */
  settledOrders: number
  /** 已取消订单数 */
  cancelledOrders: number
}



/**
 * API响应结果
 */
export interface ApiResult<T> {
  /** 响应码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
}

/**
 * 实际结算金额项
 */
export interface ActualSettlementItem {
  /** 订单ID */
  payid: string
  /** 订单编号显示 */
  orderDisplay: string
  /** 系统计算的推广劳务费 */
  calculatedFee: number
  /** 实际结算金额 */
  actualAmount: number
}

/**
 * 批量结算请求
 */
export interface BatchSettleRequest {
  /** 订单结算项列表 */
  items: ActualSettlementItem[]
}