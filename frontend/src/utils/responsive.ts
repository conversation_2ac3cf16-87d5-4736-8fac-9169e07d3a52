import { type Breakpoint, BREAKPOINTS } from '@/hooks/useBreakpoint'

/**
 * 响应式值类型
 */
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>

/**
 * 响应式间距配置
 */
export interface ResponsiveSpacing {
  /** 内边距 */
  padding: {
    /** 页面容器内边距 */
    container: string
    /** 卡片内边距 */
    card: string
    /** 按钮内边距 */
    button: string
    /** 表单字段内边距 */
    input: string
  }
  /** 外边距 */
  margin: {
    /** 组件间距 */
    component: string
    /** 区块间距 */
    section: string
  }
  /** 间隙 */
  gap: {
    /** 网格间隙 */
    grid: string
    /** 弹性布局间隙 */
    flex: string
  }
}

/**
 * 响应式文字配置
 */
export interface ResponsiveTypography {
  /** 页面标题 */
  pageTitle: string
  /** 区块标题 */
  sectionTitle: string
  /** 卡片标题 */
  cardTitle: string
  /** 正文 */
  body: string
  /** 小文字 */
  small: string
  /** 标签 */
  label: string
  /** 按钮文字 */
  button: string
}

/**
 * 响应式布局配置
 */
export interface ResponsiveLayout {
  /** 网格列数 */
  gridCols: {
    stats: string
    charts: string
    table: string
  }
  /** 容器宽度 */
  container: string
  /** 对话框宽度 */
  dialog: string
}

/**
 * 默认响应式间距配置
 */
export const RESPONSIVE_SPACING: ResponsiveSpacing = {
  padding: {
    container: 'px-4 py-4 sm:px-6 sm:py-6',
    card: 'px-4 py-3 sm:px-6 sm:py-4',
    button: 'px-3 py-1.5 sm:px-4 sm:py-2',
    input: 'px-3 py-2 sm:px-4 sm:py-2.5'
  },
  margin: {
    component: 'mb-4 sm:mb-6',
    section: 'mb-6 sm:mb-8'
  },
  gap: {
    grid: 'gap-3 sm:gap-4 lg:gap-6',
    flex: 'gap-2 sm:gap-3 lg:gap-4'
  }
}

/**
 * 默认响应式文字配置
 */
export const RESPONSIVE_TYPOGRAPHY: ResponsiveTypography = {
  pageTitle: 'text-lg sm:text-xl lg:text-2xl',
  sectionTitle: 'text-base sm:text-lg lg:text-xl',
  cardTitle: 'text-xs sm:text-sm',
  body: 'text-sm sm:text-base',
  small: 'text-xs sm:text-sm',
  label: 'text-xs sm:text-sm',
  button: 'text-xs sm:text-sm'
}

/**
 * 默认响应式布局配置
 */
export const RESPONSIVE_LAYOUT: ResponsiveLayout = {
  gridCols: {
    stats: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
    charts: 'grid-cols-1 lg:grid-cols-2',
    table: 'grid-cols-1'
  },
  container: 'max-w-7xl mx-auto',
  dialog: 'max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl'
}

/**
 * 获取响应式类名
 * 根据断点配置生成对应的Tailwind CSS类名
 */
export const getResponsiveClassName = <T extends string>(
  values: ResponsiveValue<T>,
  prefix: string = ''
): string => {
  if (typeof values === 'string') {
    return values
  }

  const classes: string[] = []
  const breakpoints: Breakpoint[] = ['sm', 'md', 'lg', 'xl', '2xl']

  // 添加默认值（无断点前缀）
  if (values.sm) {
    classes.push(`${prefix}${values.sm}`)
  }

  // 添加断点特定的值
  breakpoints.forEach(breakpoint => {
    if (values[breakpoint]) {
      classes.push(`${breakpoint}:${prefix}${values[breakpoint]}`)
    }
  })

  return classes.join(' ')
}

/**
 * 获取响应式数值
 * 根据当前屏幕宽度返回对应的数值
 */
export const getResponsiveValue = <T>(
  values: ResponsiveValue<T>,
  currentWidth: number
): T => {
  if (typeof values !== 'object' || values === null) {
    return values as T
  }

  // 按断点大小排序，从大到小查找匹配的值
  const sortedBreakpoints: Array<[Breakpoint, number]> = [
    ['2xl', BREAKPOINTS['2xl']],
    ['xl', BREAKPOINTS.xl],
    ['lg', BREAKPOINTS.lg],
    ['md', BREAKPOINTS.md],
    ['sm', BREAKPOINTS.sm]
  ]

  for (const [breakpoint, minWidth] of sortedBreakpoints) {
    if (currentWidth >= minWidth && values[breakpoint] !== undefined) {
      return values[breakpoint] as T
    }
  }

  // 如果没有找到匹配的断点，返回最小断点的值或第一个可用值
  return (values.sm || Object.values(values)[0]) as T
}

/**
 * 响应式图表高度配置
 */
export const RESPONSIVE_CHART_HEIGHTS = {
  sm: 200,
  md: 250,
  lg: 300,
  xl: 400,
  '2xl': 450
} as const

/**
 * 获取响应式图表高度
 */
export const getResponsiveChartHeight = (currentWidth: number): number => {
  return getResponsiveValue(RESPONSIVE_CHART_HEIGHTS, currentWidth)
}

/**
 * 响应式表格配置
 */
export interface ResponsiveTableConfig {
  /** 是否显示某列 */
  showColumn: (columnKey: string, breakpoint: Breakpoint) => boolean
  /** 获取表格单元格类名 */
  getCellClassName: (breakpoint: Breakpoint) => string
  /** 获取表格头部类名 */
  getHeaderClassName: (breakpoint: Breakpoint) => string
}

/**
 * 默认响应式表格配置
 */
export const createResponsiveTableConfig = (
  columnConfig: Record<string, Breakpoint[]>
): ResponsiveTableConfig => {
  return {
    showColumn: (columnKey: string, breakpoint: Breakpoint) => {
      const visibleBreakpoints = columnConfig[columnKey] || ['sm']
      const breakpointOrder: Breakpoint[] = ['sm', 'md', 'lg', 'xl', '2xl']
      const currentIndex = breakpointOrder.indexOf(breakpoint)
      
      return visibleBreakpoints.some(bp => {
        const bpIndex = breakpointOrder.indexOf(bp)
        return bpIndex <= currentIndex
      })
    },
    getCellClassName: (breakpoint: Breakpoint) => {
      const baseClasses = 'px-2 py-2'
      const responsiveClasses = {
        sm: 'sm:px-3 sm:py-2',
        md: 'md:px-4 md:py-3',
        lg: 'lg:px-4 lg:py-3',
        xl: 'xl:px-6 xl:py-4',
        '2xl': '2xl:px-6 2xl:py-4'
      }
      return `${baseClasses} ${responsiveClasses[breakpoint] || ''}`
    },
    getHeaderClassName: (breakpoint: Breakpoint) => {
      const baseClasses = 'text-xs font-medium text-gray-500 uppercase tracking-wider'
      const responsiveClasses = {
        sm: 'sm:text-sm',
        md: 'md:text-sm',
        lg: 'lg:text-sm',
        xl: 'xl:text-sm',
        '2xl': '2xl:text-base'
      }
      return `${baseClasses} ${responsiveClasses[breakpoint] || ''}`
    }
  }
}

/**
 * 响应式图标大小配置
 */
export const RESPONSIVE_ICON_SIZES = {
  small: 'w-3 h-3 sm:w-4 sm:h-4',
  medium: 'w-4 h-4 sm:w-5 sm:h-5',
  large: 'w-5 h-5 sm:w-6 sm:h-6'
} as const

/**
 * 获取响应式图标大小类名
 */
export const getResponsiveIconSize = (size: keyof typeof RESPONSIVE_ICON_SIZES): string => {
  return RESPONSIVE_ICON_SIZES[size]
}
