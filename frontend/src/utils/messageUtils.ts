/**
 * 统一的消息提示工具函数
 * 
 * 替代原生的 alert() 和 confirm()，使用项目统一的 UI 组件
 * 基于 Shadcn UI 的 Toast 和 ConfirmDialog 组件
 */

import { toast } from '@/hooks/useToast'

/**
 * 消息类型枚举
 */
export enum MessageType {
  SUCCESS = 'success',
  ERROR = 'destructive', 
  WARNING = 'default',
  INFO = 'default'
}

/**
 * 确认对话框配置接口
 */
export interface ConfirmOptions {
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
}

/**
 * 确认对话框状态管理
 */
class ConfirmDialogManager {
  private static instance: ConfirmDialogManager
  private currentResolve: ((value: boolean) => void) | null = null
  private isOpen = false

  static getInstance(): ConfirmDialogManager {
    if (!ConfirmDialogManager.instance) {
      ConfirmDialogManager.instance = new ConfirmDialogManager()
    }
    return ConfirmDialogManager.instance
  }

  /**
   * 显示确认对话框
   */
  async show(options: ConfirmOptions): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.isOpen) {
        // 如果已有对话框打开，直接返回 false
        resolve(false)
        return
      }

      this.currentResolve = resolve
      this.isOpen = true

      // 创建确认对话框元素
      this.createConfirmDialog(options)
    })
  }

  /**
   * 创建确认对话框
   */
  private createConfirmDialog(options: ConfirmOptions) {
    // 创建对话框容器
    const dialogContainer = document.createElement('div')
    dialogContainer.id = 'confirm-dialog-container'
    document.body.appendChild(dialogContainer)

    // 创建对话框内容
    const dialogHTML = `
      <div class="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0">
        <div class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg">
          <div class="flex flex-col space-y-2 text-center sm:text-left">
            <div class="flex items-center gap-3">
              ${options.variant === 'destructive' ? `
                <div class="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
              ` : ''}
              <div class="flex-1">
                <h2 class="text-lg font-semibold ${options.variant === 'destructive' ? 'text-red-900' : ''}">${options.title}</h2>
                <p class="text-sm text-muted-foreground mt-2">${options.description}</p>
              </div>
            </div>
          </div>
          <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <button id="confirm-cancel" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 mt-2 sm:mt-0">
              ${options.cancelText || '取消'}
            </button>
            <button id="confirm-ok" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${options.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : 'bg-primary text-primary-foreground hover:bg-primary/90'} h-10 px-4 py-2">
              ${options.confirmText || '确认'}
            </button>
          </div>
        </div>
      </div>
    `

    dialogContainer.innerHTML = dialogHTML

    // 绑定事件
    const cancelBtn = dialogContainer.querySelector('#confirm-cancel')
    const okBtn = dialogContainer.querySelector('#confirm-ok')

    cancelBtn?.addEventListener('click', () => this.handleResult(false))
    okBtn?.addEventListener('click', () => this.handleResult(true))

    // 点击背景关闭
    const overlay = dialogContainer.querySelector('.fixed.inset-0')
    overlay?.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.handleResult(false)
      }
    })

    // ESC 键关闭
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.handleResult(false)
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
    document.addEventListener('keydown', handleKeyDown)
  }

  /**
   * 处理对话框结果
   */
  private handleResult(result: boolean) {
    if (this.currentResolve) {
      this.currentResolve(result)
      this.currentResolve = null
    }

    this.isOpen = false

    // 移除对话框
    const container = document.getElementById('confirm-dialog-container')
    if (container) {
      container.remove()
    }
  }
}

/**
 * 消息提示工具类
 */
export class MessageUtils {
  /**
   * 显示成功消息
   * @param message 消息内容
   * @param title 标题（可选）
   * @param duration 显示时长（毫秒）
   */
  static success(message: string, title?: string, duration = 4000) {
    toast({
      title: title || '成功',
      description: message,
      variant: MessageType.SUCCESS,
      duration
    })
  }

  /**
   * 显示错误消息
   * @param message 消息内容
   * @param title 标题（可选）
   * @param duration 显示时长（毫秒）
   */
  static error(message: string, title?: string, duration = 4000) {
    toast({
      title: title || '错误',
      description: message,
      variant: MessageType.ERROR,
      duration
    })
  }

  /**
   * 显示警告消息
   * @param message 消息内容
   * @param title 标题（可选）
   * @param duration 显示时长（毫秒）
   */
  static warning(message: string, title?: string, duration = 4000) {
    toast({
      title: title || '警告',
      description: message,
      variant: MessageType.WARNING,
      duration
    })
  }

  /**
   * 显示信息消息
   * @param message 消息内容
   * @param title 标题（可选）
   * @param duration 显示时长（毫秒）
   */
  static info(message: string, title?: string, duration = 4000) {
    toast({
      title: title || '提示',
      description: message,
      variant: MessageType.INFO,
      duration
    })
  }

  /**
   * 显示确认对话框
   * @param message 确认消息
   * @param title 标题（可选）
   * @param options 其他选项
   * @returns Promise<boolean> 用户选择结果
   */
  static async confirm(
    message: string, 
    title = '确认操作',
    options: Partial<ConfirmOptions> = {}
  ): Promise<boolean> {
    const confirmOptions: ConfirmOptions = {
      title,
      description: message,
      confirmText: options.confirmText || '确认',
      cancelText: options.cancelText || '取消',
      variant: options.variant || 'default'
    }

    return ConfirmDialogManager.getInstance().show(confirmOptions)
  }

  /**
   * 显示删除确认对话框
   * @param message 确认消息
   * @param title 标题（可选）
   * @returns Promise<boolean> 用户选择结果
   */
  static async confirmDelete(
    message: string,
    title = '确认删除'
  ): Promise<boolean> {
    return this.confirm(message, title, {
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    })
  }

  /**
   * 替代原生 alert() 的方法
   * @param message 消息内容
   * @param type 消息类型
   */
  static alert(message: string, type: MessageType = MessageType.INFO) {
    switch (type) {
      case MessageType.SUCCESS:
        this.success(message)
        break
      case MessageType.ERROR:
        this.error(message)
        break
      case MessageType.WARNING:
        this.warning(message)
        break
      default:
        this.info(message)
        break
    }
  }
}

// 导出便捷方法
export const { success, error, warning, info, confirm, confirmDelete, alert } = MessageUtils
